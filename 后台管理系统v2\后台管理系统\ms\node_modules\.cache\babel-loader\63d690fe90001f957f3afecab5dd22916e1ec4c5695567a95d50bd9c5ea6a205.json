{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport axios from 'axios';\nimport router from '../router';\nimport { ElMessage } from 'element-plus';\n\n// 配置axios默认值\nconst api = axios.create({\n  baseURL: 'http://127.0.0.1:3000',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器 - 添加认证头\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理常见错误\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  const {\n    response\n  } = error;\n  if (response) {\n    switch (response.status) {\n      case 401:\n        localStorage.removeItem('token');\n        localStorage.removeItem('userId');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('userInfo');\n        if (router.currentRoute.value.path !== '/login') {\n          router.push('/login');\n          ElMessage({\n            message: '登录已过期，请重新登录',\n            type: 'error',\n            duration: 3000\n          });\n        }\n        break;\n      case 403:\n        ElMessage({\n          message: '您没有权限执行此操作',\n          type: 'error',\n          duration: 3000\n        });\n        break;\n      case 404:\n        if (response.config.url.includes('/api/')) {\n          ElMessage({\n            message: '请求的资源不存在',\n            type: 'error',\n            duration: 3000\n          });\n        }\n        break;\n      default:\n        ElMessage({\n          message: response.data.message || '请求失败',\n          type: 'error',\n          duration: 3000\n        });\n    }\n  } else {\n    ElMessage({\n      message: '网络错误，请检查您的网络连接',\n      type: 'error',\n      duration: 3000\n    });\n  }\n  return Promise.reject(error);\n});\n\n// API接口定义\nexport const authAPI = {\n  // 用户登录\n  login: data => api.post('/api/auth/login', data),\n  // 用户注册\n  register: data => api.post('/api/auth/register', data),\n  // 忘记密码\n  forgotPassword: data => api.post('/api/auth/forgot-password', data),\n  // 重置密码\n  resetPassword: data => api.post('/api/auth/reset-password', data),\n  // 获取当前用户信息\n  getCurrentUser: () => api.get('/api/auth/me'),\n  // 修改密码\n  changePassword: data => api.post('/api/auth/change-password', data)\n};\nexport const categoryAPI = {\n  // 获取所有分类\n  getAll: params => api.get('/api/categories', {\n    params\n  }),\n  // 获取单个分类\n  getById: id => api.get(`/api/categories/${id}`),\n  // 创建分类\n  create: data => api.post('/api/categories', data),\n  // 更新分类\n  update: (id, data) => api.put(`/api/categories/${id}`, data),\n  // 删除分类\n  delete: id => api.delete(`/api/categories/${id}`)\n};\nexport const itemAPI = {\n  // 获取所有子项\n  getAll: params => api.get('/api/items', {\n    params\n  }),\n  // 根据分类ID获取子项\n  getByCategory: categoryId => api.get(`/api/items/category/${categoryId}`),\n  // 获取单个子项\n  getById: id => api.get(`/api/items/${id}`),\n  // 创建子项\n  create: data => api.post('/api/items', data, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }),\n  // 更新子项\n  update: (id, data) => api.put(`/api/items/${id}`, data, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }),\n  // 删除子项\n  delete: id => api.delete(`/api/items/${id}`)\n};\nexport const orderAPI = {\n  // 获取所有订单\n  getAll: params => api.get('/api/orders', {\n    params\n  }),\n  // 根据订单号获取订单\n  getByNumber: orderNumber => api.get(`/api/orders/number/${orderNumber}`),\n  // 获取单个订单\n  getById: id => api.get(`/api/orders/${id}`),\n  // 创建订单\n  create: data => api.post('/api/orders', data),\n  // 更新订单\n  update: (id, data) => api.put(`/api/orders/${id}`, data),\n  // 更新订单状态\n  updateStatus: (id, data) => api.put(`/api/orders/${id}/status`, data),\n  // 删除订单\n  delete: id => api.delete(`/api/orders/${id}`)\n};\nexport const userAPI = {\n  // 用户登录（对应 router.post('/login', userController.login)）\n  login: data => api.post('/api/users/login', data),\n  // 获取所有用户（对应 router.get('/', userController.getAllUsers)）\n  getAll: params => api.get('/api/users', {\n    params\n  }),\n  // 获取单个用户信息（对应 router.get('/:id', userController.getUserById)）\n  getById: id => api.get(`/api/users/${id}`),\n  // 创建用户（对应 router.post('/', userController.createUser)）\n  create: data => api.post('/api/users', data),\n  // 更新用户信息（对应 router.put('/:id', userController.updateUser)）\n  update: (id, data) => api.put(`/api/users/${id}`, data),\n  // 重置用户密码\n  resetPassword: (id, data) => api.put(`/api/users/${id}/password`, data),\n  // 删除用户（对应 router.delete('/:id', userController.deleteUser)）\n  delete: id => api.delete(`/api/users/${id}`)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "router", "ElMessage", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "error", "Promise", "reject", "response", "status", "removeItem", "currentRoute", "value", "path", "push", "message", "type", "duration", "url", "includes", "data", "authAPI", "login", "post", "register", "forgotPassword", "resetPassword", "getCurrentUser", "get", "changePassword", "categoryAPI", "getAll", "params", "getById", "id", "update", "put", "delete", "itemAPI", "getByCategory", "categoryId", "orderAPI", "getByNumber", "orderNumber", "updateStatus", "userAPI"], "sources": ["D:/admin/202506/乐高/乐高后台/后台管理系统v2/后台管理系统/ms/src/utils/api.js"], "sourcesContent": ["import axios from 'axios'\r\nimport router from '../router'\r\nimport { ElMessage } from 'element-plus'\r\n\r\n// 配置axios默认值\r\nconst api = axios.create({\r\n  baseURL: 'http://127.0.0.1:3000',\r\n  timeout: 10000,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n})\r\n\r\n// 请求拦截器 - 添加认证头\r\napi.interceptors.request.use(\r\n  config => {\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器 - 处理常见错误\r\napi.interceptors.response.use(\r\n  response => {\r\n    return response\r\n  },\r\n  error => {\r\n    const { response } = error\r\n    if (response) {\r\n      switch (response.status) {\r\n        case 401:\r\n          localStorage.removeItem('token')\r\n          localStorage.removeItem('userId')\r\n          localStorage.removeItem('userRole')\r\n          localStorage.removeItem('userInfo')\r\n          \r\n          if (router.currentRoute.value.path !== '/login') {\r\n            router.push('/login')\r\n            ElMessage({\r\n              message: '登录已过期，请重新登录',\r\n              type: 'error',\r\n              duration: 3000\r\n            })\r\n          }\r\n          break\r\n          \r\n        case 403:\r\n          ElMessage({\r\n            message: '您没有权限执行此操作',\r\n            type: 'error',\r\n            duration: 3000\r\n          })\r\n          break\r\n\r\n        case 404:\r\n          if (response.config.url.includes('/api/')) {\r\n            ElMessage({\r\n              message: '请求的资源不存在',\r\n              type: 'error',\r\n              duration: 3000\r\n            })\r\n          }\r\n          break\r\n          \r\n        default:\r\n          ElMessage({\r\n            message: response.data.message || '请求失败',\r\n            type: 'error',\r\n            duration: 3000\r\n          })\r\n      }\r\n    } else {\r\n      ElMessage({\r\n        message: '网络错误，请检查您的网络连接',\r\n        type: 'error',\r\n        duration: 3000\r\n      })\r\n    }\r\n    \r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// API接口定义\r\nexport const authAPI = {\r\n  // 用户登录\r\n  login: (data) => api.post('/api/auth/login', data),\r\n  // 用户注册\r\n  register: (data) => api.post('/api/auth/register', data),\r\n  // 忘记密码\r\n  forgotPassword: (data) => api.post('/api/auth/forgot-password', data),\r\n  // 重置密码\r\n  resetPassword: (data) => api.post('/api/auth/reset-password', data),\r\n  // 获取当前用户信息\r\n  getCurrentUser: () => api.get('/api/auth/me'),\r\n  // 修改密码\r\n  changePassword: (data) => api.post('/api/auth/change-password', data)\r\n}\r\n\r\nexport const categoryAPI = {\r\n  // 获取所有分类\r\n  getAll: (params) => api.get('/api/categories', { params }),\r\n  // 获取单个分类\r\n  getById: (id) => api.get(`/api/categories/${id}`),\r\n  // 创建分类\r\n  create: (data) => api.post('/api/categories', data),\r\n  // 更新分类\r\n  update: (id, data) => api.put(`/api/categories/${id}`, data),\r\n  // 删除分类\r\n  delete: (id) => api.delete(`/api/categories/${id}`)\r\n}\r\n\r\nexport const itemAPI = {\r\n  // 获取所有子项\r\n  getAll: (params) => api.get('/api/items', { params }),\r\n  // 根据分类ID获取子项\r\n  getByCategory: (categoryId) => api.get(`/api/items/category/${categoryId}`),\r\n  // 获取单个子项\r\n  getById: (id) => api.get(`/api/items/${id}`),\r\n  // 创建子项\r\n  create: (data) => api.post('/api/items', data, {\r\n    headers: { 'Content-Type': 'multipart/form-data' }\r\n  }),\r\n  // 更新子项\r\n  update: (id, data) => api.put(`/api/items/${id}`, data, {\r\n    headers: { 'Content-Type': 'multipart/form-data' }\r\n  }),\r\n  // 删除子项\r\n  delete: (id) => api.delete(`/api/items/${id}`)\r\n}\r\n\r\nexport const orderAPI = {\r\n  // 获取所有订单\r\n  getAll: (params) => api.get('/api/orders', { params }),\r\n  // 根据订单号获取订单\r\n  getByNumber: (orderNumber) => api.get(`/api/orders/number/${orderNumber}`),\r\n  // 获取单个订单\r\n  getById: (id) => api.get(`/api/orders/${id}`),\r\n  // 创建订单\r\n  create: (data) => api.post('/api/orders', data),\r\n  // 更新订单\r\n  update: (id, data) => api.put(`/api/orders/${id}`, data),\r\n  // 更新订单状态\r\n  updateStatus: (id, data) => api.put(`/api/orders/${id}/status`, data),\r\n  // 删除订单\r\n  delete: (id) => api.delete(`/api/orders/${id}`)\r\n}\r\n\r\nexport const userAPI = {\r\n  // 用户登录（对应 router.post('/login', userController.login)）\r\n  login: (data) => api.post('/api/users/login', data),\r\n\r\n  // 获取所有用户（对应 router.get('/', userController.getAllUsers)）\r\n  getAll: (params) => api.get('/api/users', { params }),\r\n\r\n  // 获取单个用户信息（对应 router.get('/:id', userController.getUserById)）\r\n  getById: (id) => api.get(`/api/users/${id}`),\r\n\r\n  // 创建用户（对应 router.post('/', userController.createUser)）\r\n  create: (data) => api.post('/api/users', data),\r\n\r\n  // 更新用户信息（对应 router.put('/:id', userController.updateUser)）\r\n  update: (id, data) => api.put(`/api/users/${id}`, data),\r\n\r\n  // 重置用户密码\r\n  resetPassword: (id, data) => api.put(`/api/users/${id}/password`, data),\r\n\r\n  // 删除用户（对应 router.delete('/:id', userController.deleteUser)）\r\n  delete: (id) => api.delete(`/api/users/${id}`)\r\n}\r\n\r\nexport default api\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,SAAS,QAAQ,cAAc;;AAExC;AACA,MAAMC,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC;EACvBC,OAAO,EAAE,uBAAuB;EAChCC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1BC,MAAM,IAAI;EACR,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUK,KAAK,EAAE;EACrD;EACA,OAAOD,MAAM;AACf,CAAC,EACDI,KAAK,IAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACK,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC3BQ,QAAQ,IAAI;EACV,OAAOA,QAAQ;AACjB,CAAC,EACDH,KAAK,IAAI;EACP,MAAM;IAAEG;EAAS,CAAC,GAAGH,KAAK;EAC1B,IAAIG,QAAQ,EAAE;IACZ,QAAQA,QAAQ,CAACC,MAAM;MACrB,KAAK,GAAG;QACNN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;QAChCP,YAAY,CAACO,UAAU,CAAC,QAAQ,CAAC;QACjCP,YAAY,CAACO,UAAU,CAAC,UAAU,CAAC;QACnCP,YAAY,CAACO,UAAU,CAAC,UAAU,CAAC;QAEnC,IAAInB,MAAM,CAACoB,YAAY,CAACC,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;UAC/CtB,MAAM,CAACuB,IAAI,CAAC,QAAQ,CAAC;UACrBtB,SAAS,CAAC;YACRuB,OAAO,EAAE,aAAa;YACtBC,IAAI,EAAE,OAAO;YACbC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QACA;MAEF,KAAK,GAAG;QACNzB,SAAS,CAAC;UACRuB,OAAO,EAAE,YAAY;UACrBC,IAAI,EAAE,OAAO;UACbC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MAEF,KAAK,GAAG;QACN,IAAIT,QAAQ,CAACP,MAAM,CAACiB,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;UACzC3B,SAAS,CAAC;YACRuB,OAAO,EAAE,UAAU;YACnBC,IAAI,EAAE,OAAO;YACbC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QACA;MAEF;QACEzB,SAAS,CAAC;UACRuB,OAAO,EAAEP,QAAQ,CAACY,IAAI,CAACL,OAAO,IAAI,MAAM;UACxCC,IAAI,EAAE,OAAO;UACbC,QAAQ,EAAE;QACZ,CAAC,CAAC;IACN;EACF,CAAC,MAAM;IACLzB,SAAS,CAAC;MACRuB,OAAO,EAAE,gBAAgB;MACzBC,IAAI,EAAE,OAAO;MACbC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EAEA,OAAOX,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMgB,OAAO,GAAG;EACrB;EACAC,KAAK,EAAGF,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,iBAAiB,EAAEH,IAAI,CAAC;EAClD;EACAI,QAAQ,EAAGJ,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,oBAAoB,EAAEH,IAAI,CAAC;EACxD;EACAK,cAAc,EAAGL,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,2BAA2B,EAAEH,IAAI,CAAC;EACrE;EACAM,aAAa,EAAGN,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,0BAA0B,EAAEH,IAAI,CAAC;EACnE;EACAO,cAAc,EAAEA,CAAA,KAAMlC,GAAG,CAACmC,GAAG,CAAC,cAAc,CAAC;EAC7C;EACAC,cAAc,EAAGT,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,2BAA2B,EAAEH,IAAI;AACtE,CAAC;AAED,OAAO,MAAMU,WAAW,GAAG;EACzB;EACAC,MAAM,EAAGC,MAAM,IAAKvC,GAAG,CAACmC,GAAG,CAAC,iBAAiB,EAAE;IAAEI;EAAO,CAAC,CAAC;EAC1D;EACAC,OAAO,EAAGC,EAAE,IAAKzC,GAAG,CAACmC,GAAG,CAAC,mBAAmBM,EAAE,EAAE,CAAC;EACjD;EACAxC,MAAM,EAAG0B,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,iBAAiB,EAAEH,IAAI,CAAC;EACnD;EACAe,MAAM,EAAEA,CAACD,EAAE,EAAEd,IAAI,KAAK3B,GAAG,CAAC2C,GAAG,CAAC,mBAAmBF,EAAE,EAAE,EAAEd,IAAI,CAAC;EAC5D;EACAiB,MAAM,EAAGH,EAAE,IAAKzC,GAAG,CAAC4C,MAAM,CAAC,mBAAmBH,EAAE,EAAE;AACpD,CAAC;AAED,OAAO,MAAMI,OAAO,GAAG;EACrB;EACAP,MAAM,EAAGC,MAAM,IAAKvC,GAAG,CAACmC,GAAG,CAAC,YAAY,EAAE;IAAEI;EAAO,CAAC,CAAC;EACrD;EACAO,aAAa,EAAGC,UAAU,IAAK/C,GAAG,CAACmC,GAAG,CAAC,uBAAuBY,UAAU,EAAE,CAAC;EAC3E;EACAP,OAAO,EAAGC,EAAE,IAAKzC,GAAG,CAACmC,GAAG,CAAC,cAAcM,EAAE,EAAE,CAAC;EAC5C;EACAxC,MAAM,EAAG0B,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,YAAY,EAAEH,IAAI,EAAE;IAC7CvB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAsB;EACnD,CAAC,CAAC;EACF;EACAsC,MAAM,EAAEA,CAACD,EAAE,EAAEd,IAAI,KAAK3B,GAAG,CAAC2C,GAAG,CAAC,cAAcF,EAAE,EAAE,EAAEd,IAAI,EAAE;IACtDvB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAsB;EACnD,CAAC,CAAC;EACF;EACAwC,MAAM,EAAGH,EAAE,IAAKzC,GAAG,CAAC4C,MAAM,CAAC,cAAcH,EAAE,EAAE;AAC/C,CAAC;AAED,OAAO,MAAMO,QAAQ,GAAG;EACtB;EACAV,MAAM,EAAGC,MAAM,IAAKvC,GAAG,CAACmC,GAAG,CAAC,aAAa,EAAE;IAAEI;EAAO,CAAC,CAAC;EACtD;EACAU,WAAW,EAAGC,WAAW,IAAKlD,GAAG,CAACmC,GAAG,CAAC,sBAAsBe,WAAW,EAAE,CAAC;EAC1E;EACAV,OAAO,EAAGC,EAAE,IAAKzC,GAAG,CAACmC,GAAG,CAAC,eAAeM,EAAE,EAAE,CAAC;EAC7C;EACAxC,MAAM,EAAG0B,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,aAAa,EAAEH,IAAI,CAAC;EAC/C;EACAe,MAAM,EAAEA,CAACD,EAAE,EAAEd,IAAI,KAAK3B,GAAG,CAAC2C,GAAG,CAAC,eAAeF,EAAE,EAAE,EAAEd,IAAI,CAAC;EACxD;EACAwB,YAAY,EAAEA,CAACV,EAAE,EAAEd,IAAI,KAAK3B,GAAG,CAAC2C,GAAG,CAAC,eAAeF,EAAE,SAAS,EAAEd,IAAI,CAAC;EACrE;EACAiB,MAAM,EAAGH,EAAE,IAAKzC,GAAG,CAAC4C,MAAM,CAAC,eAAeH,EAAE,EAAE;AAChD,CAAC;AAED,OAAO,MAAMW,OAAO,GAAG;EACrB;EACAvB,KAAK,EAAGF,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,kBAAkB,EAAEH,IAAI,CAAC;EAEnD;EACAW,MAAM,EAAGC,MAAM,IAAKvC,GAAG,CAACmC,GAAG,CAAC,YAAY,EAAE;IAAEI;EAAO,CAAC,CAAC;EAErD;EACAC,OAAO,EAAGC,EAAE,IAAKzC,GAAG,CAACmC,GAAG,CAAC,cAAcM,EAAE,EAAE,CAAC;EAE5C;EACAxC,MAAM,EAAG0B,IAAI,IAAK3B,GAAG,CAAC8B,IAAI,CAAC,YAAY,EAAEH,IAAI,CAAC;EAE9C;EACAe,MAAM,EAAEA,CAACD,EAAE,EAAEd,IAAI,KAAK3B,GAAG,CAAC2C,GAAG,CAAC,cAAcF,EAAE,EAAE,EAAEd,IAAI,CAAC;EAEvD;EACAM,aAAa,EAAEA,CAACQ,EAAE,EAAEd,IAAI,KAAK3B,GAAG,CAAC2C,GAAG,CAAC,cAAcF,EAAE,WAAW,EAAEd,IAAI,CAAC;EAEvE;EACAiB,MAAM,EAAGH,EAAE,IAAKzC,GAAG,CAAC4C,MAAM,CAAC,cAAcH,EAAE,EAAE;AAC/C,CAAC;AAED,eAAezC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}