<template>
  <div class="order-list">
    <div class="page-header">
      <h2>订单管理</h2>
    </div>

    <div class="search-area">
      <el-card shadow="never">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="订单号">
            <el-input
              v-model="searchForm.order_number"
              placeholder="请输入订单号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="客户姓名">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入客户姓名"
              clearable
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px">
              <el-option label="待处理" value="pending" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="fetchOrders" :loading="loading">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <div class="table-container">
      <el-card shadow="never">
        <div class="table-header">
          <div class="table-title">
            <span>订单列表</span>
            <span class="count">共 {{ orders.length }} 条</span>
          </div>
        </div>

        <el-table
          :data="orders"
          v-loading="loading"
          stripe
          border
          empty-text="暂无订单数据"
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="order_number" label="订单号" width="180" show-overflow-tooltip />
          <el-table-column prop="name" label="客户姓名" width="120" show-overflow-tooltip />
          <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip />
          <el-table-column prop="phone" label="电话" width="130" />
          <el-table-column prop="order_items" label="订单内容" min-width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="order-items">
                {{ formatOrderItems(row.order_items) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160" align="center">
            <template #default="{ row }">
              {{ formatDate(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewOrder(row)" link>
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <el-button type="warning" size="small" @click="updateStatus(row)" link>
                <el-icon><Edit /></el-icon>
                状态
              </el-button>
              <el-button type="danger" size="small" @click="deleteOrder(row)" link>
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="orderDetailVisible" title="订单详情" width="600px">
      <div v-if="currentOrder" class="order-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ currentOrder.order_number }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentOrder.status)">
              {{ getStatusText(currentOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="客户姓名">{{ currentOrder.name }}</el-descriptions-item>
          <el-descriptions-item label="电话">{{ currentOrder.phone }}</el-descriptions-item>
          <el-descriptions-item label="邮箱" :span="2">{{ currentOrder.email }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ formatDate(currentOrder.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="订单内容" :span="2">
            <div class="order-content">
              {{ formatOrderItems(currentOrder.order_items) }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 更新状态对话框 -->
    <el-dialog v-model="statusUpdateVisible" title="更新订单状态" width="400px">
      <el-form :model="statusForm" label-width="80px">
        <el-form-item label="当前状态">
          <el-tag :type="getStatusType(statusForm.currentStatus)">
            {{ getStatusText(statusForm.currentStatus) }}
          </el-tag>
        </el-form-item>
        <el-form-item label="新状态" required>
          <el-select v-model="statusForm.newStatus" placeholder="请选择新状态" style="width: 100%">
            <el-option label="待处理" value="pending" />
            <el-option label="处理中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="statusUpdateVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUpdateStatus" :loading="updating">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, View, Edit, Delete } from '@element-plus/icons-vue'
import { orderAPI } from '@/utils/api'

const orders = ref([])
const loading = ref(false)
const updating = ref(false)
const orderDetailVisible = ref(false)
const statusUpdateVisible = ref(false)
const currentOrder = ref(null)

const searchForm = reactive({
  order_number: '',
  name: '',
  status: ''
})

const statusForm = reactive({
  orderId: null,
  currentStatus: '',
  newStatus: ''
})

const fetchOrders = async () => {
  loading.value = true
  try {
    const response = await orderAPI.getAll(searchForm)
    orders.value = response.data.data || response.data
  } catch (error) {
    ElMessage.error('获取订单列表失败')
    console.error('获取订单失败:', error)
  } finally {
    loading.value = false
  }
}

const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatOrderItems = (orderItems) => {
  if (!orderItems) return '-'
  try {
    // 如果是JSON字符串，尝试解析
    if (typeof orderItems === 'string') {
      const parsed = JSON.parse(orderItems)
      if (Array.isArray(parsed)) {
        return parsed.map(item => item.name || item.title || item).join(', ')
      }
      return orderItems
    }
    // 如果是数组
    if (Array.isArray(orderItems)) {
      return orderItems.map(item => item.name || item.title || item).join(', ')
    }
    return orderItems.toString()
  } catch (error) {
    return orderItems.toString()
  }
}

const resetSearch = () => {
  searchForm.order_number = ''
  searchForm.name = ''
  searchForm.status = ''
  fetchOrders()
}

const viewOrder = (row) => {
  currentOrder.value = row
  orderDetailVisible.value = true
}

const updateStatus = (row) => {
  statusForm.orderId = row.id
  statusForm.currentStatus = row.status
  statusForm.newStatus = row.status
  statusUpdateVisible.value = true
}

const confirmUpdateStatus = async () => {
  if (!statusForm.newStatus) {
    ElMessage.warning('请选择新状态')
    return
  }

  if (statusForm.newStatus === statusForm.currentStatus) {
    ElMessage.warning('新状态与当前状态相同')
    return
  }

  updating.value = true
  try {
    await orderAPI.updateStatus(statusForm.orderId, { status: statusForm.newStatus })
    ElMessage.success('状态更新成功')
    statusUpdateVisible.value = false
    fetchOrders()
  } catch (error) {
    ElMessage.error('状态更新失败')
    console.error('更新状态失败:', error)
  } finally {
    updating.value = false
  }
}

const deleteOrder = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除订单 "${row.order_number}" 吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    await orderAPI.delete(row.id)
    ElMessage.success('订单删除成功')
    fetchOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除订单失败:', error)
    }
  }
}

onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.order-list {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 500;
}

.search-area {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.count {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.order-items {
  max-width: 200px;
  word-break: break-all;
  line-height: 1.4;
}

.order-detail {
  padding: 10px 0;
}

.order-content {
  max-height: 120px;
  overflow-y: auto;
  line-height: 1.6;
  word-break: break-all;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table .el-button + .el-button) {
  margin-left: 8px;
}

/* 卡片样式 */
:deep(.el-card) {
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

:deep(.el-card__body) {
  padding: 20px;
}

/* 对话框样式 */
:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-descriptions) {
  margin-top: 10px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-list {
    padding: 16px;
  }

  .search-form .el-form-item {
    margin-right: 12px;
    margin-bottom: 12px;
  }

  :deep(.el-table .el-button) {
    padding: 4px 8px;
    font-size: 12px;
  }
}
</style>
