<template>
  <div class="login-container">
    <div class="login-card">
      <!-- Left side -->
      <div class="login-info">
        <div class="logo-wrapper">
         
          <div class="logo-text">
            订单管理系统
          </div>
        </div>
        
        <div class="welcome-text">
          <h2>欢迎回来</h2>
          <p>登录您的账户以继续访问系统</p>
        </div>
        
        <div class="feature-list">
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="feature-text">现代化的管理界面</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="feature-text">强大的功能模块</div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="feature-text">安全可靠的数据保护</div>
          </div>
        </div>
      </div>
      
      <!-- Right side -->
      <div class="login-form-wrapper">
        <div class="login-form-container">
          <h2 class="form-title">用户登录</h2>
          <p class="form-subtitle">请输入您的账户信息</p>
          
          <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" class="login-form">
            <el-form-item prop="username">
              <el-input 
                v-model="loginForm.username" 
                placeholder="输入账号" 
                :prefix-icon="User">
              </el-input>
            </el-form-item>
            
            <el-form-item prop="password">
              <el-input 
                v-model="loginForm.password" 
                type="password" 
                placeholder="输入密码" 
                :prefix-icon="Lock"
                show-password>
              </el-input>
            </el-form-item>
            
           
            
            <el-form-item>
              <el-button type="primary" :loading="loading" @click="handleLogin" class="login-button">
                登录
              </el-button>
            </el-form-item>
            
           
          </el-form>
        </div>
      </div>
    </div>
    
    <!-- 注册对话框 -->
    <el-dialog
      title="用户注册"
      v-model="registerDialogVisible"
      width="400px"
      center
      destroy-on-close
    >
      <el-form :model="registerForm" :rules="registerRules" ref="registerFormRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="registerForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="registerForm.password" type="password" placeholder="请输入密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请再次输入密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="registerForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="registerForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="registerForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="registerDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="registerLoading" @click="handleRegister">注册</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 忘记密码对话框 -->
    <el-dialog
      title="忘记密码"
      v-model="forgotPasswordDialogVisible"
      width="400px"
      center
      destroy-on-close
    >
      <el-form :model="forgotPasswordForm" :rules="forgotPasswordRules" ref="forgotPasswordFormRef" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="forgotPasswordForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="forgotPasswordForm.email" placeholder="请输入注册时的邮箱"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="forgotPasswordDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="forgotPasswordLoading" @click="handleForgotPassword">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, Check } from '@element-plus/icons-vue'
import { userAPI } from '@/utils/api'

const router = useRouter()
const loginFormRef = ref(null)
const loading = ref(false)

// 登录相关
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate(async (valid) => {
      if (valid) {
        loading.value = true
        
        try {
          // 使用 userAPI.login 而不是 authAPI.login
          const response = await userAPI.login({
            username: loginForm.username,
            password: loginForm.password
          })
          
          if (response.data.success) {
            const { token, data } = response.data
            localStorage.setItem('token', token)
            localStorage.setItem('userInfo', JSON.stringify(data))
            localStorage.setItem('userId', data.id)
            localStorage.setItem('userRole', data.role)

            if (loginForm.remember) {
              localStorage.setItem('rememberedUsername', loginForm.username)
            } else {
              localStorage.removeItem('rememberedUsername')
            }

            ElMessage.success('登录成功')
            router.push('/')
          } else {
            ElMessage.error(response.data.message || '登录失败')
          }
        } catch (error) {
          console.error('登录失败:', error)
          if (error.response?.status === 401) {
            ElMessage.error('用户名或密码错误')
          } else {
            ElMessage.error(error.response?.data?.message || '登录失败，请稍后重试')
          }
        } finally {
          loading.value = false
        }
      }
    })
  } catch (error) {
    loading.value = false
    ElMessage.error('表单验证失败')
  }
}

// 注册相关
const registerDialogVisible = ref(false)
const registerFormRef = ref(null)
const registerLoading = ref(false)

const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  name: '',
  email: '',
  phone: '',
  student_id: '',
  role: 'user'  // 默认注册为普通用户
})

const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { validator: validatePass, trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const showRegister = () => {
  registerDialogVisible.value = true
}

const handleRegister = async () => {
  if (!registerFormRef.value) return
  
  try {
    await registerFormRef.value.validate(async (valid) => {
      if (valid) {
        registerLoading.value = true
        
        try {
          const { confirmPassword, ...registerData } = registerForm
          
          const response = await authAPI.register(registerData)
          
          ElMessage.success('注册成功，请登录')
          registerDialogVisible.value = false
          
          loginForm.username = registerForm.username
          loginForm.password = ''
        } catch (error) {
          console.error('注册失败:', error)
          ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试')
        } finally {
          registerLoading.value = false
        }
      }
    })
  } catch (error) {
    registerLoading.value = false
    ElMessage.error('表单验证失败')
  }
}

// 忘记密码相关
const forgotPasswordDialogVisible = ref(false)
const forgotPasswordFormRef = ref(null)
const forgotPasswordLoading = ref(false)

const forgotPasswordForm = reactive({
  username: '',
  email: ''
})

const forgotPasswordRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

const showForgotPassword = () => {
  forgotPasswordDialogVisible.value = true
}

const handleForgotPassword = async () => {
  if (!forgotPasswordFormRef.value) return
  
  try {
    await forgotPasswordFormRef.value.validate(async (valid) => {
      if (valid) {
        forgotPasswordLoading.value = true
        
        try {
          const response = await authAPI.forgotPassword(forgotPasswordForm)
          
          ElMessage.success('重置密码链接已发送到您的邮箱')
          forgotPasswordDialogVisible.value = false
        } catch (error) {
          console.error('忘记密码请求失败:', error)
          ElMessage.error(error.response?.data?.message || '操作失败，请稍后重试')
        } finally {
          forgotPasswordLoading.value = false
        }
      }
    })
  } catch (error) {
    forgotPasswordLoading.value = false
    ElMessage.error('表单验证失败')
  }
}

// 检查是否有记住的用户名
const checkRememberedUsername = () => {
  const rememberedUsername = localStorage.getItem('rememberedUsername')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
}

// 组件挂载时检查记住的用户名
checkRememberedUsername()
</script>

<style scoped>
.login-container {
  height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color:rgb(124, 181, 239);
}

.login-card {
  width: 1000px;
  height: 600px;
  display: flex;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);
}

/* Left side */
.login-info {
  width: 50%;
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  padding: 40px;
  display: flex;
  flex-direction: column;
  color: white;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 60px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 24px;
  color: #409EFF;
}

.logo-img {
  width: 24px;
  height: 24px;
  margin-right: 4px;
  object-fit: contain;
}

.logo-text {
  font-size: 20px;
  font-weight: bold;
}

.welcome-text {
  margin-bottom: 60px;
}

.welcome-text h2 {
  font-size: 32px;
  margin-bottom: 12px;
  font-weight: 600;
}

.welcome-text p {
  font-size: 16px;
  opacity: 0.8;
}

.feature-list {
  margin-top: auto;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.feature-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.feature-text {
  font-size: 16px;
}

/* Right side */
.login-form-wrapper {
  width: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-form-container {
  width: 100%;
  max-width: 320px;
}

.form-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
}

.form-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 30px;
  text-align: center;
}

.login-form :deep(.el-input__wrapper) {
  padding: 0 15px;
  height: 50px;
  box-shadow: 0 0 0 1px #e4e7ed inset;
}

.login-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.forgot-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 14px;
}

.login-button {
  width: 100%;
  height: 50px;
  border-radius: 6px;
  font-size: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
  margin-bottom: 20px;
}

.register-link {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.register-link a {
  color: #409EFF;
  text-decoration: none;
  margin-left: 5px;
}

/* Responsive */
@media (max-width: 992px) {
  .login-card {
    width: 90%;
    height: auto;
    flex-direction: column;
  }
  
  .login-info,
  .login-form-wrapper {
    width: 100%;
    padding: 30px;
  }
  
  .login-info {
    padding-bottom: 40px;
  }
  
  .welcome-text {
    margin-bottom: 30px;
  }
  
  .feature-list {
    margin-top: 0;
  }
}
</style> 
