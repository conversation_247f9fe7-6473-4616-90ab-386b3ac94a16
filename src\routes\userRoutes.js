const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');

// 用户登录（不需要认证）
router.post('/login', userController.login);

// 获取所有用户
router.get('/', userController.getAllUsers);

// 获取单个用户信息
router.get('/:id', userController.getUserById);

// 创建用户
router.post('/', userController.createUser);

// 更新用户信息
router.put('/:id', userController.updateUser);

// 重置用户密码
router.put('/:id/password', userController.resetPassword);

// 删除用户
router.delete('/:id', userController.deleteUser);

module.exports = router;