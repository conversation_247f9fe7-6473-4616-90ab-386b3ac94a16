{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, openBlock as _openBlock, createBlock as _createBlock, withDirectives as _withDirectives, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"order-list\"\n};\nconst _hoisted_2 = {\n  class: \"search-area\"\n};\nconst _hoisted_3 = {\n  class: \"table-container\"\n};\nconst _hoisted_4 = {\n  class: \"table-header\"\n};\nconst _hoisted_5 = {\n  class: \"table-title\"\n};\nconst _hoisted_6 = {\n  class: \"count\"\n};\nconst _hoisted_7 = {\n  class: \"order-items\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"order-detail\"\n};\nconst _hoisted_9 = {\n  class: \"order-content\"\n};\nconst _hoisted_10 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_View = _resolveComponent(\"View\");\n  const _component_Edit = _resolveComponent(\"Edit\");\n  const _component_Delete = _resolveComponent(\"Delete\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"page-header\"\n  }, [_createElementVNode(\"h2\", null, \"订单管理\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_card, {\n    shadow: \"never\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      inline: true,\n      model: $setup.searchForm,\n      class: \"search-form\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"订单号\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchForm.order_number,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchForm.order_number = $event),\n          placeholder: \"请输入订单号\",\n          clearable: \"\",\n          style: {\n            \"width\": \"200px\"\n          }\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"客户姓名\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchForm.name,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchForm.name = $event),\n          placeholder: \"请输入客户姓名\",\n          clearable: \"\",\n          style: {\n            \"width\": \"150px\"\n          }\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"状态\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.searchForm.status,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.searchForm.status = $event),\n          placeholder: \"请选择状态\",\n          clearable: \"\",\n          style: {\n            \"width\": \"120px\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"待处理\",\n            value: \"pending\"\n          }), _createVNode(_component_el_option, {\n            label: \"处理中\",\n            value: \"processing\"\n          }), _createVNode(_component_el_option, {\n            label: \"已完成\",\n            value: \"completed\"\n          }), _createVNode(_component_el_option, {\n            label: \"已取消\",\n            value: \"cancelled\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.fetchOrders,\n          loading: $setup.loading\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode($setup[\"Search\"])]),\n            _: 1 /* STABLE */\n          }), _cache[7] || (_cache[7] = _createTextVNode(\" 搜索 \"))]),\n          _: 1 /* STABLE */,\n          __: [7]\n        }, 8 /* PROPS */, [\"loading\"]), _createVNode(_component_el_button, {\n          onClick: $setup.resetSearch\n        }, {\n          default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"重置\")])),\n          _: 1 /* STABLE */,\n          __: [8]\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_card, {\n    shadow: \"never\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", null, \"订单列表\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_6, \"共 \" + _toDisplayString($setup.orders.length) + \" 条\", 1 /* TEXT */)])]), _withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.orders,\n      stripe: \"\",\n      border: \"\",\n      \"empty-text\": \"暂无订单数据\",\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        prop: \"id\",\n        label: \"ID\",\n        width: \"80\",\n        align: \"center\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"order_number\",\n        label: \"订单号\",\n        width: \"180\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"name\",\n        label: \"客户姓名\",\n        width: \"120\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"email\",\n        label: \"邮箱\",\n        width: \"180\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"phone\",\n        label: \"电话\",\n        width: \"130\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"order_items\",\n        label: \"订单内容\",\n        \"min-width\": \"200\",\n        \"show-overflow-tooltip\": \"\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [_createElementVNode(\"div\", _hoisted_7, _toDisplayString(_ctx.formatOrderItems(row.order_items)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"status\",\n        label: \"状态\",\n        width: \"100\",\n        align: \"center\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusType(row.status),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(row.status)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"created_at\",\n        label: \"创建时间\",\n        width: \"160\",\n        align: \"center\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [_createTextVNode(_toDisplayString($setup.formatDate(row.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"200\",\n        fixed: \"right\",\n        align: \"center\"\n      }, {\n        default: _withCtx(({\n          row\n        }) => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          size: \"small\",\n          onClick: $event => _ctx.viewOrder(row),\n          link: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_View)]),\n            _: 1 /* STABLE */\n          }), _cache[10] || (_cache[10] = _createTextVNode(\" 查看 \"))]),\n          _: 2 /* DYNAMIC */,\n          __: [10]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"warning\",\n          size: \"small\",\n          onClick: $event => $setup.updateStatus(row),\n          link: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Edit)]),\n            _: 1 /* STABLE */\n          }), _cache[11] || (_cache[11] = _createTextVNode(\" 状态 \"))]),\n          _: 2 /* DYNAMIC */,\n          __: [11]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          type: \"danger\",\n          size: \"small\",\n          onClick: $event => $setup.deleteOrder(row),\n          link: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Delete)]),\n            _: 1 /* STABLE */\n          }), _cache[12] || (_cache[12] = _createTextVNode(\" 删除 \"))]),\n          _: 2 /* DYNAMIC */,\n          __: [12]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]])]),\n    _: 1 /* STABLE */\n  })]), _createCommentVNode(\" 订单详情对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: _ctx.orderDetailVisible,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => _ctx.orderDetailVisible = $event),\n    title: \"订单详情\",\n    width: \"600px\"\n  }, {\n    default: _withCtx(() => [_ctx.currentOrder ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"订单号\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.currentOrder.order_number), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"状态\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusType(_ctx.currentOrder.status)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(_ctx.currentOrder.status)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"客户姓名\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.currentOrder.name), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"电话\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.currentOrder.phone), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"邮箱\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.currentOrder.email), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"创建时间\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDate(_ctx.currentOrder.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"订单内容\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, _toDisplayString(_ctx.formatOrderItems(_ctx.currentOrder.order_items)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 更新状态对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: _ctx.statusUpdateVisible,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => _ctx.statusUpdateVisible = $event),\n    title: \"更新订单状态\",\n    width: \"400px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_10, [_createVNode(_component_el_button, {\n      onClick: _cache[5] || (_cache[5] = $event => _ctx.statusUpdateVisible = false)\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [13]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _ctx.confirmUpdateStatus,\n      loading: _ctx.updating\n    }, {\n      default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"确定\")])),\n      _: 1 /* STABLE */,\n      __: [14]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: _ctx.statusForm,\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"当前状态\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusType(_ctx.statusForm.currentStatus)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(_ctx.statusForm.currentStatus)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"新状态\",\n        required: \"\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: _ctx.statusForm.newStatus,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => _ctx.statusForm.newStatus = $event),\n          placeholder: \"请选择新状态\",\n          style: {\n            \"width\": \"100%\"\n          }\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"待处理\",\n            value: \"pending\"\n          }), _createVNode(_component_el_option, {\n            label: \"处理中\",\n            value: \"processing\"\n          }), _createVNode(_component_el_option, {\n            label: \"已完成\",\n            value: \"completed\"\n          }), _createVNode(_component_el_option, {\n            label: \"已取消\",\n            value: \"cancelled\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_card", "shadow", "_component_el_form", "inline", "model", "$setup", "searchForm", "_component_el_form_item", "label", "_component_el_input", "order_number", "$event", "placeholder", "clearable", "style", "name", "_component_el_select", "status", "_component_el_option", "value", "_component_el_button", "type", "onClick", "fetchOrders", "loading", "_component_el_icon", "resetSearch", "_cache", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_toDisplayString", "orders", "length", "_createBlock", "_component_el_table", "data", "stripe", "border", "_component_el_table_column", "prop", "width", "align", "default", "_withCtx", "row", "_hoisted_7", "_ctx", "formatOrderItems", "order_items", "_component_el_tag", "getStatusType", "size", "getStatusText", "formatDate", "created_at", "fixed", "viewOrder", "link", "_component_View", "updateStatus", "_component_Edit", "deleteOrder", "_component_Delete", "_createCommentVNode", "_component_el_dialog", "orderDetailVisible", "title", "currentOrder", "_hoisted_8", "_component_el_descriptions", "column", "_component_el_descriptions_item", "phone", "span", "email", "_hoisted_9", "statusUpdateVisible", "footer", "_hoisted_10", "confirmUpdateStatus", "updating", "statusForm", "currentStatus", "required", "newStatus"], "sources": ["D:\\admin\\202506\\乐高\\乐高后台\\后台管理系统v2\\后台管理系统\\ms\\src\\views\\orders\\OrderList.vue"], "sourcesContent": ["<template>\n  <div class=\"order-list\">\n    <div class=\"page-header\">\n      <h2>订单管理</h2>\n    </div>\n\n    <div class=\"search-area\">\n      <el-card shadow=\"never\">\n        <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\n          <el-form-item label=\"订单号\">\n            <el-input\n              v-model=\"searchForm.order_number\"\n              placeholder=\"请输入订单号\"\n              clearable\n              style=\"width: 200px\"\n            />\n          </el-form-item>\n          <el-form-item label=\"客户姓名\">\n            <el-input\n              v-model=\"searchForm.name\"\n              placeholder=\"请输入客户姓名\"\n              clearable\n              style=\"width: 150px\"\n            />\n          </el-form-item>\n          <el-form-item label=\"状态\">\n            <el-select v-model=\"searchForm.status\" placeholder=\"请选择状态\" clearable style=\"width: 120px\">\n              <el-option label=\"待处理\" value=\"pending\" />\n              <el-option label=\"处理中\" value=\"processing\" />\n              <el-option label=\"已完成\" value=\"completed\" />\n              <el-option label=\"已取消\" value=\"cancelled\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"fetchOrders\" :loading=\"loading\">\n              <el-icon><Search /></el-icon>\n              搜索\n            </el-button>\n            <el-button @click=\"resetSearch\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-card>\n    </div>\n\n    <div class=\"table-container\">\n      <el-card shadow=\"never\">\n        <div class=\"table-header\">\n          <div class=\"table-title\">\n            <span>订单列表</span>\n            <span class=\"count\">共 {{ orders.length }} 条</span>\n          </div>\n        </div>\n\n        <el-table\n          :data=\"orders\"\n          v-loading=\"loading\"\n          stripe\n          border\n          empty-text=\"暂无订单数据\"\n          style=\"width: 100%\"\n        >\n          <el-table-column prop=\"id\" label=\"ID\" width=\"80\" align=\"center\" />\n          <el-table-column prop=\"order_number\" label=\"订单号\" width=\"180\" show-overflow-tooltip />\n          <el-table-column prop=\"name\" label=\"客户姓名\" width=\"120\" show-overflow-tooltip />\n          <el-table-column prop=\"email\" label=\"邮箱\" width=\"180\" show-overflow-tooltip />\n          <el-table-column prop=\"phone\" label=\"电话\" width=\"130\" />\n          <el-table-column prop=\"order_items\" label=\"订单内容\" min-width=\"200\" show-overflow-tooltip>\n            <template #default=\"{ row }\">\n              <div class=\"order-items\">\n                {{ formatOrderItems(row.order_items) }}\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-tag :type=\"getStatusType(row.status)\" size=\"small\">\n                {{ getStatusText(row.status) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\" align=\"center\">\n            <template #default=\"{ row }\">\n              {{ formatDate(row.created_at) }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"200\" fixed=\"right\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"primary\" size=\"small\" @click=\"viewOrder(row)\" link>\n                <el-icon><View /></el-icon>\n                查看\n              </el-button>\n              <el-button type=\"warning\" size=\"small\" @click=\"updateStatus(row)\" link>\n                <el-icon><Edit /></el-icon>\n                状态\n              </el-button>\n              <el-button type=\"danger\" size=\"small\" @click=\"deleteOrder(row)\" link>\n                <el-icon><Delete /></el-icon>\n                删除\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-card>\n    </div>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog v-model=\"orderDetailVisible\" title=\"订单详情\" width=\"600px\">\n      <div v-if=\"currentOrder\" class=\"order-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"订单号\">{{ currentOrder.order_number }}</el-descriptions-item>\n          <el-descriptions-item label=\"状态\">\n            <el-tag :type=\"getStatusType(currentOrder.status)\">\n              {{ getStatusText(currentOrder.status) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"客户姓名\">{{ currentOrder.name }}</el-descriptions-item>\n          <el-descriptions-item label=\"电话\">{{ currentOrder.phone }}</el-descriptions-item>\n          <el-descriptions-item label=\"邮箱\" :span=\"2\">{{ currentOrder.email }}</el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\" :span=\"2\">{{ formatDate(currentOrder.created_at) }}</el-descriptions-item>\n          <el-descriptions-item label=\"订单内容\" :span=\"2\">\n            <div class=\"order-content\">\n              {{ formatOrderItems(currentOrder.order_items) }}\n            </div>\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n    </el-dialog>\n\n    <!-- 更新状态对话框 -->\n    <el-dialog v-model=\"statusUpdateVisible\" title=\"更新订单状态\" width=\"400px\">\n      <el-form :model=\"statusForm\" label-width=\"80px\">\n        <el-form-item label=\"当前状态\">\n          <el-tag :type=\"getStatusType(statusForm.currentStatus)\">\n            {{ getStatusText(statusForm.currentStatus) }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"新状态\" required>\n          <el-select v-model=\"statusForm.newStatus\" placeholder=\"请选择新状态\" style=\"width: 100%\">\n            <el-option label=\"待处理\" value=\"pending\" />\n            <el-option label=\"处理中\" value=\"processing\" />\n            <el-option label=\"已完成\" value=\"completed\" />\n            <el-option label=\"已取消\" value=\"cancelled\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"statusUpdateVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmUpdateStatus\" :loading=\"updating\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Plus, Search } from '@element-plus/icons-vue'\nimport { orderAPI } from '@/utils/api'\n\nconst orders = ref([])\nconst loading = ref(false)\n\nconst searchForm = reactive({\n  orderNumber: '',\n  status: ''\n})\n\nconst fetchOrders = async () => {\n  loading.value = true\n  try {\n    const response = await orderAPI.getAll(searchForm)\n    orders.value = response.data.data || response.data\n  } catch (error) {\n    ElMessage.error('获取订单列表失败')\n  } finally {\n    loading.value = false\n  }\n}\n\nconst getStatusType = (status) => {\n  const types = {\n    pending: 'warning',\n    processing: 'primary',\n    completed: 'success',\n    cancelled: 'danger'\n  }\n  return types[status] || 'info'\n}\n\nconst getStatusText = (status) => {\n  const texts = {\n    pending: '待处理',\n    processing: '处理中',\n    completed: '已完成',\n    cancelled: '已取消'\n  }\n  return texts[status] || status\n}\n\nconst formatDate = (date) => {\n  return new Date(date).toLocaleString()\n}\n\nconst resetSearch = () => {\n  searchForm.orderNumber = ''\n  searchForm.status = ''\n  fetchOrders()\n}\n\nconst showAddDialog = () => {\n  // 实现添加订单逻辑\n  ElMessage.info('添加订单功能待实现')\n}\n\nconst editOrder = (row) => {\n  // 实现编辑订单逻辑\n  ElMessage.info('编辑订单功能待实现')\n}\n\nconst updateStatus = async (row) => {\n  // 实现更新状态逻辑\n  ElMessage.info('更新状态功能待实现')\n}\n\nconst deleteOrder = async (row) => {\n  try {\n    await ElMessageBox.confirm('确定要删除这个订单吗？', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    })\n    \n    await orderAPI.delete(row.id)\n    ElMessage.success('删除成功')\n    fetchOrders()\n  } catch (error) {\n    if (error !== 'cancel') {\n      ElMessage.error('删除失败')\n    }\n  }\n}\n\nonMounted(() => {\n  fetchOrders()\n})\n</script>\n\n<style scoped>\n.order-list {\n  padding: 24px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e8eaec;\n}\n\n.search-area {\n  background-color: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n  margin-bottom: 16px;\n}\n\n.table-container {\n  background-color: #fff;\n  border-radius: 6px;\n  overflow: hidden;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAKhBA,KAAK,EAAC;AAAa;;EAsCnBA,KAAK,EAAC;AAAiB;;EAEnBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAa;;EAEhBA,KAAK,EAAC;AAAO;;EAmBZA,KAAK,EAAC;AAAa;;;EAuCPA,KAAK,EAAC;;;EAapBA,KAAK,EAAC;AAAe;;EA0BxBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;uBAjJjCC,mBAAA,CAuJM,OAvJNC,UAuJM,G,4BAtJJC,mBAAA,CAEM;IAFDH,KAAK,EAAC;EAAa,IACtBG,mBAAA,CAAa,YAAT,MAAI,E,qBAGVA,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJC,YAAA,CAkCUC,kBAAA;IAlCDC,MAAM,EAAC;EAAO;sBACrB,MAgCU,CAhCVF,YAAA,CAgCUG,kBAAA;MAhCAC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,MAAA,CAAAC,UAAU;MAAEZ,KAAK,EAAC;;wBAChD,MAOe,CAPfK,YAAA,CAOeQ,uBAAA;QAPDC,KAAK,EAAC;MAAK;0BACvB,MAKE,CALFT,YAAA,CAKEU,mBAAA;sBAJSJ,MAAA,CAAAC,UAAU,CAACI,YAAY;qEAAvBL,MAAA,CAAAC,UAAU,CAACI,YAAY,GAAAC,MAAA;UAChCC,WAAW,EAAC,QAAQ;UACpBC,SAAS,EAAT,EAAS;UACTC,KAAoB,EAApB;YAAA;UAAA;;;UAGJf,YAAA,CAOeQ,uBAAA;QAPDC,KAAK,EAAC;MAAM;0BACxB,MAKE,CALFT,YAAA,CAKEU,mBAAA;sBAJSJ,MAAA,CAAAC,UAAU,CAACS,IAAI;qEAAfV,MAAA,CAAAC,UAAU,CAACS,IAAI,GAAAJ,MAAA;UACxBC,WAAW,EAAC,SAAS;UACrBC,SAAS,EAAT,EAAS;UACTC,KAAoB,EAApB;YAAA;UAAA;;;UAGJf,YAAA,CAOeQ,uBAAA;QAPDC,KAAK,EAAC;MAAI;0BACtB,MAKY,CALZT,YAAA,CAKYiB,oBAAA;sBALQX,MAAA,CAAAC,UAAU,CAACW,MAAM;qEAAjBZ,MAAA,CAAAC,UAAU,CAACW,MAAM,GAAAN,MAAA;UAAEC,WAAW,EAAC,OAAO;UAACC,SAAS,EAAT,EAAS;UAACC,KAAoB,EAApB;YAAA;UAAA;;4BACnE,MAAyC,CAAzCf,YAAA,CAAyCmB,oBAAA;YAA9BV,KAAK,EAAC,KAAK;YAACW,KAAK,EAAC;cAC7BpB,YAAA,CAA4CmB,oBAAA;YAAjCV,KAAK,EAAC,KAAK;YAACW,KAAK,EAAC;cAC7BpB,YAAA,CAA2CmB,oBAAA;YAAhCV,KAAK,EAAC,KAAK;YAACW,KAAK,EAAC;cAC7BpB,YAAA,CAA2CmB,oBAAA;YAAhCV,KAAK,EAAC,KAAK;YAACW,KAAK,EAAC;;;;;UAGjCpB,YAAA,CAMeQ,uBAAA;0BALb,MAGY,CAHZR,YAAA,CAGYqB,oBAAA;UAHDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEjB,MAAA,CAAAkB,WAAW;UAAGC,OAAO,EAAEnB,MAAA,CAAAmB;;4BACvD,MAA6B,CAA7BzB,YAAA,CAA6B0B,kBAAA;8BAApB,MAAU,CAAV1B,YAAA,CAAUM,MAAA,Y;;yDAAU,MAE/B,G;;;wCACAN,YAAA,CAA8CqB,oBAAA;UAAlCE,OAAK,EAAEjB,MAAA,CAAAqB;QAAW;4BAAE,MAAEC,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;;;QAM1C9B,mBAAA,CA2DM,OA3DN+B,UA2DM,GA1DJ7B,YAAA,CAyDUC,kBAAA;IAzDDC,MAAM,EAAC;EAAO;sBACrB,MAKM,CALNJ,mBAAA,CAKM,OALNgC,UAKM,GAJJhC,mBAAA,CAGM,OAHNiC,UAGM,G,0BAFJjC,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAAkD,QAAlDkC,UAAkD,EAA9B,IAAE,GAAAC,gBAAA,CAAG3B,MAAA,CAAA4B,MAAM,CAACC,MAAM,IAAG,IAAE,gB,oCAI/CC,YAAA,CAgDWC,mBAAA;MA/CRC,IAAI,EAAEhC,MAAA,CAAA4B,MAAM;MAEbK,MAAM,EAAN,EAAM;MACNC,MAAM,EAAN,EAAM;MACN,YAAU,EAAC,QAAQ;MACnBzB,KAAmB,EAAnB;QAAA;MAAA;;wBAEA,MAAkE,CAAlEf,YAAA,CAAkEyC,0BAAA;QAAjDC,IAAI,EAAC,IAAI;QAACjC,KAAK,EAAC,IAAI;QAACkC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UACvD5C,YAAA,CAAqFyC,0BAAA;QAApEC,IAAI,EAAC,cAAc;QAACjC,KAAK,EAAC,KAAK;QAACkC,KAAK,EAAC,KAAK;QAAC,uBAAqB,EAArB;UAC7D3C,YAAA,CAA8EyC,0BAAA;QAA7DC,IAAI,EAAC,MAAM;QAACjC,KAAK,EAAC,MAAM;QAACkC,KAAK,EAAC,KAAK;QAAC,uBAAqB,EAArB;UACtD3C,YAAA,CAA6EyC,0BAAA;QAA5DC,IAAI,EAAC,OAAO;QAACjC,KAAK,EAAC,IAAI;QAACkC,KAAK,EAAC,KAAK;QAAC,uBAAqB,EAArB;UACrD3C,YAAA,CAAuDyC,0BAAA;QAAtCC,IAAI,EAAC,OAAO;QAACjC,KAAK,EAAC,IAAI;QAACkC,KAAK,EAAC;UAC/C3C,YAAA,CAMkByC,0BAAA;QANDC,IAAI,EAAC,aAAa;QAACjC,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;;QACpDoC,OAAO,EAAAC,QAAA,CAChB,CAEM;UAHcC;QAAG,OACvBjD,mBAAA,CAEM,OAFNkD,UAEM,EAAAf,gBAAA,CADDgB,IAAA,CAAAC,gBAAgB,CAACH,GAAG,CAACI,WAAW,kB;;UAIzCnD,YAAA,CAMkByC,0BAAA;QANDC,IAAI,EAAC,QAAQ;QAACjC,KAAK,EAAC,IAAI;QAACkC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;QAC/CC,OAAO,EAAAC,QAAA,CAChB,CAES;UAHWC;QAAG,OACvB/C,YAAA,CAESoD,iBAAA;UAFA9B,IAAI,EAAEhB,MAAA,CAAA+C,aAAa,CAACN,GAAG,CAAC7B,MAAM;UAAGoC,IAAI,EAAC;;4BAC7C,MAA+B,C,kCAA5BhD,MAAA,CAAAiD,aAAa,CAACR,GAAG,CAAC7B,MAAM,kB;;;;UAIjClB,YAAA,CAIkByC,0BAAA;QAJDC,IAAI,EAAC,YAAY;QAACjC,KAAK,EAAC,MAAM;QAACkC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;QACrDC,OAAO,EAAAC,QAAA,CAChB,CAAgC;UADZC;QAAG,O,kCACpBzC,MAAA,CAAAkD,UAAU,CAACT,GAAG,CAACU,UAAU,kB;;UAGhCzD,YAAA,CAekByC,0BAAA;QAfDhC,KAAK,EAAC,IAAI;QAACkC,KAAK,EAAC,KAAK;QAACe,KAAK,EAAC,OAAO;QAACd,KAAK,EAAC;;QAC/CC,OAAO,EAAAC,QAAA,CAChB,CAGY;UAJQC;QAAG,OACvB/C,YAAA,CAGYqB,oBAAA;UAHDC,IAAI,EAAC,SAAS;UAACgC,IAAI,EAAC,OAAO;UAAE/B,OAAK,EAAAX,MAAA,IAAEqC,IAAA,CAAAU,SAAS,CAACZ,GAAG;UAAGa,IAAI,EAAJ;;4BAC7D,MAA2B,CAA3B5D,YAAA,CAA2B0B,kBAAA;8BAAlB,MAAQ,CAAR1B,YAAA,CAAQ6D,eAAA,E;;2DAAU,MAE7B,G;;;0DACA7D,YAAA,CAGYqB,oBAAA;UAHDC,IAAI,EAAC,SAAS;UAACgC,IAAI,EAAC,OAAO;UAAE/B,OAAK,EAAAX,MAAA,IAAEN,MAAA,CAAAwD,YAAY,CAACf,GAAG;UAAGa,IAAI,EAAJ;;4BAChE,MAA2B,CAA3B5D,YAAA,CAA2B0B,kBAAA;8BAAlB,MAAQ,CAAR1B,YAAA,CAAQ+D,eAAA,E;;2DAAU,MAE7B,G;;;0DACA/D,YAAA,CAGYqB,oBAAA;UAHDC,IAAI,EAAC,QAAQ;UAACgC,IAAI,EAAC,OAAO;UAAE/B,OAAK,EAAAX,MAAA,IAAEN,MAAA,CAAA0D,WAAW,CAACjB,GAAG;UAAGa,IAAI,EAAJ;;4BAC9D,MAA6B,CAA7B5D,YAAA,CAA6B0B,kBAAA;8BAApB,MAAU,CAAV1B,YAAA,CAAUiE,iBAAA,E;;2DAAU,MAE/B,G;;;;;;;wDA3CO3D,MAAA,CAAAmB,OAAO,E;;QAkDxByC,mBAAA,aAAgB,EAChBlE,YAAA,CAoBYmE,oBAAA;gBApBQlB,IAAA,CAAAmB,kBAAkB;+DAAlBnB,IAAA,CAAAmB,kBAAkB,GAAAxD,MAAA;IAAEyD,KAAK,EAAC,MAAM;IAAC1B,KAAK,EAAC;;sBACzD,MAkBM,CAlBKM,IAAA,CAAAqB,YAAY,I,cAAvB1E,mBAAA,CAkBM,OAlBN2E,UAkBM,GAjBJvE,YAAA,CAgBkBwE,0BAAA;MAhBAC,MAAM,EAAE,CAAC;MAAEjC,MAAM,EAAN;;wBAC3B,MAAwF,CAAxFxC,YAAA,CAAwF0E,+BAAA;QAAlEjE,KAAK,EAAC;MAAK;0BAAC,MAA+B,C,kCAA5BwC,IAAA,CAAAqB,YAAY,CAAC3D,YAAY,iB;;UAC9DX,YAAA,CAIuB0E,+BAAA;QAJDjE,KAAK,EAAC;MAAI;0BAC9B,MAES,CAFTT,YAAA,CAESoD,iBAAA;UAFA9B,IAAI,EAAEhB,MAAA,CAAA+C,aAAa,CAACJ,IAAA,CAAAqB,YAAY,CAACpD,MAAM;;4BAC9C,MAAwC,C,kCAArCZ,MAAA,CAAAiD,aAAa,CAACN,IAAA,CAAAqB,YAAY,CAACpD,MAAM,kB;;;;UAGxClB,YAAA,CAAiF0E,+BAAA;QAA3DjE,KAAK,EAAC;MAAM;0BAAC,MAAuB,C,kCAApBwC,IAAA,CAAAqB,YAAY,CAACtD,IAAI,iB;;UACvDhB,YAAA,CAAgF0E,+BAAA;QAA1DjE,KAAK,EAAC;MAAI;0BAAC,MAAwB,C,kCAArBwC,IAAA,CAAAqB,YAAY,CAACK,KAAK,iB;;UACtD3E,YAAA,CAA0F0E,+BAAA;QAApEjE,KAAK,EAAC,IAAI;QAAEmE,IAAI,EAAE;;0BAAG,MAAwB,C,kCAArB3B,IAAA,CAAAqB,YAAY,CAACO,KAAK,iB;;UAChE7E,YAAA,CAA6G0E,+BAAA;QAAvFjE,KAAK,EAAC,MAAM;QAAEmE,IAAI,EAAE;;0BAAG,MAAyC,C,kCAAtCtE,MAAA,CAAAkD,UAAU,CAACP,IAAA,CAAAqB,YAAY,CAACb,UAAU,kB;;UAClFzD,YAAA,CAIuB0E,+BAAA;QAJDjE,KAAK,EAAC,MAAM;QAAEmE,IAAI,EAAE;;0BACxC,MAEM,CAFN9E,mBAAA,CAEM,OAFNgF,UAEM,EAAA7C,gBAAA,CADDgB,IAAA,CAAAC,gBAAgB,CAACD,IAAA,CAAAqB,YAAY,CAACnB,WAAW,kB;;;;;;qCAOtDe,mBAAA,aAAgB,EAChBlE,YAAA,CAsBYmE,oBAAA;gBAtBQlB,IAAA,CAAA8B,mBAAmB;+DAAnB9B,IAAA,CAAA8B,mBAAmB,GAAAnE,MAAA;IAAEyD,KAAK,EAAC,QAAQ;IAAC1B,KAAK,EAAC;;IAgBjDqC,MAAM,EAAAlC,QAAA,CACf,MAGO,CAHPhD,mBAAA,CAGO,QAHPmF,WAGO,GAFLjF,YAAA,CAA8DqB,oBAAA;MAAlDE,OAAK,EAAAK,MAAA,QAAAA,MAAA,MAAAhB,MAAA,IAAEqC,IAAA,CAAA8B,mBAAmB;;wBAAU,MAAEnD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAClD5B,YAAA,CAAyFqB,oBAAA;MAA9EC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAE0B,IAAA,CAAAiC,mBAAmB;MAAGzD,OAAO,EAAEwB,IAAA,CAAAkC;;wBAAU,MAAEvD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAlBjF,MAcU,CAdV5B,YAAA,CAcUG,kBAAA;MAdAE,KAAK,EAAE4C,IAAA,CAAAmC,UAAU;MAAE,aAAW,EAAC;;wBACvC,MAIe,CAJfpF,YAAA,CAIeQ,uBAAA;QAJDC,KAAK,EAAC;MAAM;0BACxB,MAES,CAFTT,YAAA,CAESoD,iBAAA;UAFA9B,IAAI,EAAEhB,MAAA,CAAA+C,aAAa,CAACJ,IAAA,CAAAmC,UAAU,CAACC,aAAa;;4BACnD,MAA6C,C,kCAA1C/E,MAAA,CAAAiD,aAAa,CAACN,IAAA,CAAAmC,UAAU,CAACC,aAAa,kB;;;;UAG7CrF,YAAA,CAOeQ,uBAAA;QAPDC,KAAK,EAAC,KAAK;QAAC6E,QAAQ,EAAR;;0BACxB,MAKY,CALZtF,YAAA,CAKYiB,oBAAA;sBALQgC,IAAA,CAAAmC,UAAU,CAACG,SAAS;qEAApBtC,IAAA,CAAAmC,UAAU,CAACG,SAAS,GAAA3E,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAACE,KAAmB,EAAnB;YAAA;UAAA;;4BAC7D,MAAyC,CAAzCf,YAAA,CAAyCmB,oBAAA;YAA9BV,KAAK,EAAC,KAAK;YAACW,KAAK,EAAC;cAC7BpB,YAAA,CAA4CmB,oBAAA;YAAjCV,KAAK,EAAC,KAAK;YAACW,KAAK,EAAC;cAC7BpB,YAAA,CAA2CmB,oBAAA;YAAhCV,KAAK,EAAC,KAAK;YAACW,KAAK,EAAC;cAC7BpB,YAAA,CAA2CmB,oBAAA;YAAhCV,KAAK,EAAC,KAAK;YAACW,KAAK,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}