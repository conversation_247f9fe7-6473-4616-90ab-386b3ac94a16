const { pool } = require('./db');

// 初始化数据库表
const initDatabase = async () => {
  try {
    const connection = await pool.getConnection();
    

    
    // 1. 创建分类表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category VARCHAR(100) NOT NULL COMMENT '类别',
        content TEXT COMMENT '内容',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 2. 创建子元素表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category_id INT NOT NULL COMMENT '分类ID',
        name VARCHAR(200) COMMENT '名称',
        thumbnail VARCHAR(255) NOT NULL COMMENT '缩略图',
        front_image VARCHAR(255) NOT NULL COMMENT '前置图',
        back_image VARCHAR(255) NOT NULL COMMENT '后置图',
        status VARCHAR(50) COMMENT '状态',
        content TEXT COMMENT '内容',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 3. 创建订单表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_number VARCHAR(100) NOT NULL UNIQUE COMMENT '订单号',
        name VARCHAR(100) COMMENT '姓名',
        email VARCHAR(100) COMMENT '邮箱',
        phone VARCHAR(20) COMMENT '电话',
        order_items LONGTEXT COMMENT '下单的东西',
        status VARCHAR(50) COMMENT '状态',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 4. 创建用户表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
        password VARCHAR(255) NOT NULL COMMENT '密码',
        email VARCHAR(100) COMMENT '邮箱',
        phone VARCHAR(20) COMMENT '电话',
        role ENUM('admin', 'user') NOT NULL DEFAULT 'user' COMMENT '角色',
        status TINYINT(1) DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
        avatar VARCHAR(255) COMMENT '头像',
        last_login TIMESTAMP COMMENT '最后登录时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 插入默认管理员账户
    await connection.query(`
      INSERT IGNORE INTO users (username, password, email, role, status) 
      VALUES ('admin', '$2a$10$N9qo8uLOickgx2ZMRZoMye.IjPeOXxVOvJdy.Gn.1eeX6.dJuB4S6', '<EMAIL>', 'admin', 1)
    `);

    console.log('数据库表初始化成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库表初始化失败:', error);
    return false;
  }
};

module.exports = {
  initDatabase
}; 
