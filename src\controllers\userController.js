const User = require('../models/userModel');
const jwt = require('jsonwebtoken');

// 获取所有用户
exports.getAllUsers = async (req, res) => {
  try {
    const filters = {
      username: req.query.username,
      email: req.query.email,
      role: req.query.role,
      status: req.query.status !== undefined ? parseInt(req.query.status) : undefined,
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10
    };
    
    // 获取总记录数
    const countFilters = { ...filters };
    delete countFilters.page;
    delete countFilters.limit;
    const allUsers = await User.findAll(countFilters);
    const total = allUsers.length;
    
    // 获取分页数据
    const users = await User.findAll(filters);
    
    res.status(200).json({
      success: true,
      count: total,
      data: users
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
};

// 获取单个用户
exports.getUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error.message
    });
  }
};

// 创建用户
exports.createUser = async (req, res) => {
  try {
    const { username, password, email, phone, role, status } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名和密码'
      });
    }
    
    // 检查用户名是否已存在
    const existingUser = await User.findByUsername(username);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该用户名已被使用'
      });
    }
    
    const userId = await User.create({
      username,
      password,
      email,
      phone,
      role,
      status
    });
    
    const newUser = await User.findById(userId);
    
    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: newUser
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
};

// 更新用户
exports.updateUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    // 如果要更新用户名，检查是否已存在
    if (req.body.username && req.body.username !== user.username) {
      const existingUser = await User.findByUsername(req.body.username);
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: '该用户名已被使用'
        });
      }
    }
    
    const updated = await User.update(req.params.id, req.body);
    
    if (updated) {
      const updatedUser = await User.findById(req.params.id);
      res.status(200).json({
        success: true,
        message: '用户更新成功',
        data: updatedUser
      });
    } else {
      res.status(500).json({
        success: false,
        message: '用户更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新用户失败',
      error: error.message
    });
  }
};

// 重置用户密码
exports.resetPassword = async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({
        success: false,
        message: '请提供新密码'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: '密码长度不能少于6位'
      });
    }

    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }


    const updated = await User.updatePassword(req.params.id, password);

    if (updated) {
      res.status(200).json({
        success: true,
        message: '密码重置成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '密码重置失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '重置密码失败',
      error: error.message
    });
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    const deleted = await User.delete(req.params.id);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '用户删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '用户删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
};

// 用户登录
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名和密码'
      });
    }
    
    const user = await User.findByUsername(username);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    if (user.status === 0) {
      return res.status(401).json({
        success: false,
        message: '账户已被禁用'
      });
    }
    
    
    if (password !== user.password) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    // 更新最后登录时间
    await User.updateLastLogin(user.id);
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: '24h' }
    );
    
    // 移除密码字段
    delete user.password;
    
    res.status(200).json({
      success: true,
      message: '登录成功',
      token,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '登录失败',
      error: error.message
    });
  }
};
