const { pool } = require('../config/db');

class Order {
  // 创建新订单
  static async create(orderData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO orders (order_number, name, email, phone, order_items, status) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          orderData.order_number,
          orderData.name,
          orderData.email,
          orderData.phone,
          orderData.order_items,
          orderData.status
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建订单失败:', error);
      throw error;
    }
  }

  // 获取所有订单
  static async findAll(filters = {}) {
    try {
      let query = 'SELECT * FROM orders WHERE 1=1';
      const queryParams = [];

      // 添加搜索条件
      if (filters.order_number) {
        query += ' AND order_number LIKE ?';
        queryParams.push(`%${filters.order_number}%`);
      }

      if (filters.name) {
        query += ' AND name LIKE ?';
        queryParams.push(`%${filters.name}%`);
      }

      if (filters.status) {
        query += ' AND status = ?';
        queryParams.push(filters.status);
      }

      // 排序
      query += ' ORDER BY created_at DESC';

      // 分页
      if (filters.page && filters.limit) {
        const offset = (filters.page - 1) * filters.limit;
        query += ' LIMIT ? OFFSET ?';
        queryParams.push(filters.limit, offset);
      }

      const [rows] = await pool.query(query, queryParams);
      return rows;
    } catch (error) {
      console.error('获取订单列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取订单
  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM orders WHERE id = ?', [id]);
      return rows[0];
    } catch (error) {
      console.error('获取订单信息失败:', error);
      throw error;
    }
  }

  // 根据订单号获取订单
  static async findByOrderNumber(orderNumber) {
    try {
      const [rows] = await pool.query('SELECT * FROM orders WHERE order_number = ?', [orderNumber]);
      return rows[0];
    } catch (error) {
      console.error('根据订单号获取订单失败:', error);
      throw error;
    }
  }

  // 更新订单信息
  static async update(id, orderData) {
    try {
      const [result] = await pool.query(
        `UPDATE orders SET name = ?, email = ?, phone = ?, order_items = ?, status = ? WHERE id = ?`,
        [
          orderData.name,
          orderData.email,
          orderData.phone,
          orderData.order_items,
          orderData.status,
          id
        ]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新订单信息失败:', error);
      throw error;
    }
  }

  // 更新订单状态
  static async updateStatus(id, status) {
    try {
      const [result] = await pool.query(
        'UPDATE orders SET status = ? WHERE id = ?',
        [status, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新订单状态失败:', error);
      throw error;
    }
  }

  // 删除订单
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM orders WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除订单失败:', error);
      throw error;
    }
  }

  // 生成订单号
  static generateOrderNumber() {
    const now = new Date();
    const timestamp = now.getTime();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD${timestamp}${random}`;
  }
}

module.exports = Order;