{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Plus, Search, View, Hide, Edit, Delete, Key } from '@element-plus/icons-vue';\nimport { userAPI } from '@/utils/api';\nexport default {\n  __name: 'UserList',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const users = ref([]);\n    const loading = ref(false);\n    const dialogVisible = ref(false);\n    const userDetailVisible = ref(false);\n    const resetPasswordVisible = ref(false);\n    const isEdit = ref(false);\n    const submitting = ref(false);\n    const resetting = ref(false);\n    const formRef = ref();\n    const currentUser = ref(null);\n    const searchForm = reactive({\n      username: '',\n      email: '',\n      role: ''\n    });\n    const userForm = reactive({\n      id: null,\n      username: '',\n      password: '',\n      email: '',\n      phone: '',\n      role: 'user',\n      status: 1\n    });\n    const passwordForm = reactive({\n      userId: null,\n      username: '',\n      newPassword: '',\n      confirmPassword: ''\n    });\n    const formRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        message: '用户名长度不能少于3位',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        message: '密码长度不能少于6位',\n        trigger: 'blur'\n      }],\n      email: [{\n        required: true,\n        message: '请输入邮箱',\n        trigger: 'blur'\n      }, {\n        type: 'email',\n        message: '请输入正确的邮箱格式',\n        trigger: 'blur'\n      }],\n      role: [{\n        required: true,\n        message: '请选择角色',\n        trigger: 'change'\n      }]\n    };\n    const fetchUsers = async () => {\n      loading.value = true;\n      try {\n        const response = await userAPI.getAll(searchForm);\n        const userData = response.data.data || response.data;\n        // 为每个用户添加密码显示状态\n        users.value = userData.map(user => ({\n          ...user,\n          showPassword: false\n        }));\n      } catch (error) {\n        ElMessage.error('获取用户列表失败');\n        console.error('获取用户失败:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n    const formatDate = date => {\n      if (!date) return '-';\n      return new Date(date).toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    };\n    const getRoleType = role => {\n      return role === 'admin' ? 'danger' : 'primary';\n    };\n    const getRoleText = role => {\n      return role === 'admin' ? '管理员' : '用户';\n    };\n    const togglePassword = row => {\n      row.showPassword = !row.showPassword;\n    };\n    const resetSearch = () => {\n      searchForm.username = '';\n      searchForm.email = '';\n      searchForm.role = '';\n      fetchUsers();\n    };\n    const viewUser = row => {\n      currentUser.value = row;\n      userDetailVisible.value = true;\n    };\n    const showAddDialog = () => {\n      isEdit.value = false;\n      dialogVisible.value = true;\n    };\n    const editUser = row => {\n      isEdit.value = true;\n      Object.assign(userForm, {\n        ...row,\n        password: '' // 编辑时不显示密码\n      });\n      dialogVisible.value = true;\n    };\n    const resetForm = () => {\n      Object.assign(userForm, {\n        id: null,\n        username: '',\n        password: '',\n        email: '',\n        phone: '',\n        role: 'user',\n        status: 1\n      });\n      if (formRef.value) {\n        formRef.value.resetFields();\n      }\n    };\n    const submitForm = async () => {\n      if (!formRef.value) return;\n      try {\n        await formRef.value.validate();\n        submitting.value = true;\n        if (isEdit.value) {\n          // 编辑时不传递密码字段\n          const {\n            password,\n            ...updateData\n          } = userForm;\n          await userAPI.update(userForm.id, updateData);\n          ElMessage.success('用户信息更新成功');\n        } else {\n          await userAPI.create(userForm);\n          ElMessage.success('用户创建成功');\n        }\n        dialogVisible.value = false;\n        fetchUsers();\n      } catch (error) {\n        ElMessage.error(isEdit.value ? '更新失败' : '创建失败');\n        console.error('提交表单失败:', error);\n      } finally {\n        submitting.value = false;\n      }\n    };\n    const resetPassword = row => {\n      passwordForm.userId = row.id;\n      passwordForm.username = row.username;\n      passwordForm.newPassword = '';\n      passwordForm.confirmPassword = '';\n      resetPasswordVisible.value = true;\n    };\n    const confirmResetPassword = async () => {\n      if (!passwordForm.newPassword) {\n        ElMessage.warning('请输入新密码');\n        return;\n      }\n      if (passwordForm.newPassword.length < 6) {\n        ElMessage.warning('密码长度不能少于6位');\n        return;\n      }\n      if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n        ElMessage.warning('两次输入的密码不一致');\n        return;\n      }\n      resetting.value = true;\n      try {\n        await userAPI.resetPassword(passwordForm.userId, {\n          password: passwordForm.newPassword\n        });\n        ElMessage.success('密码重置成功');\n        resetPasswordVisible.value = false;\n        fetchUsers();\n      } catch (error) {\n        ElMessage.error('密码重置失败');\n        console.error('重置密码失败:', error);\n      } finally {\n        resetting.value = false;\n      }\n    };\n    const deleteUser = async row => {\n      if (row.username === 'admin') {\n        ElMessage.warning('不能删除管理员账户');\n        return;\n      }\n      try {\n        await ElMessageBox.confirm(`确定要删除用户 \"${row.username}\" 吗？此操作不可恢复。`, '删除确认', {\n          confirmButtonText: '确定删除',\n          cancelButtonText: '取消',\n          type: 'warning',\n          dangerouslyUseHTMLString: false\n        });\n        await userAPI.delete(row.id);\n        ElMessage.success('用户删除成功');\n        fetchUsers();\n      } catch (error) {\n        if (error !== 'cancel') {\n          ElMessage.error('删除失败');\n          console.error('删除用户失败:', error);\n        }\n      }\n    };\n    onMounted(() => {\n      fetchUsers();\n    });\n    const __returned__ = {\n      users,\n      loading,\n      dialogVisible,\n      userDetailVisible,\n      resetPasswordVisible,\n      isEdit,\n      submitting,\n      resetting,\n      formRef,\n      currentUser,\n      searchForm,\n      userForm,\n      passwordForm,\n      formRules,\n      fetchUsers,\n      formatDate,\n      getRoleType,\n      getRoleText,\n      togglePassword,\n      resetSearch,\n      viewUser,\n      showAddDialog,\n      editUser,\n      resetForm,\n      submitForm,\n      resetPassword,\n      confirmResetPassword,\n      deleteUser,\n      ref,\n      reactive,\n      onMounted,\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get Plus() {\n        return Plus;\n      },\n      get Search() {\n        return Search;\n      },\n      get View() {\n        return View;\n      },\n      get Hide() {\n        return Hide;\n      },\n      get Edit() {\n        return Edit;\n      },\n      get Delete() {\n        return Delete;\n      },\n      get Key() {\n        return Key;\n      },\n      get userAPI() {\n        return userAPI;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "ElMessage", "ElMessageBox", "Plus", "Search", "View", "<PERSON>de", "Edit", "Delete", "Key", "userAPI", "users", "loading", "dialogVisible", "userDetailVisible", "resetPasswordVisible", "isEdit", "submitting", "resetting", "formRef", "currentUser", "searchForm", "username", "email", "role", "userForm", "id", "password", "phone", "status", "passwordForm", "userId", "newPassword", "confirmPassword", "formRules", "required", "message", "trigger", "min", "type", "fetchUsers", "value", "response", "getAll", "userData", "data", "map", "user", "showPassword", "error", "console", "formatDate", "date", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "getRoleType", "getRoleText", "togglePassword", "row", "resetSearch", "viewUser", "showAddDialog", "editUser", "Object", "assign", "resetForm", "resetFields", "submitForm", "validate", "updateData", "update", "success", "create", "resetPassword", "confirmResetPassword", "warning", "length", "deleteUser", "confirm", "confirmButtonText", "cancelButtonText", "dangerouslyUseHTMLString", "delete"], "sources": ["D:/admin/202506/乐高/乐高后台/后台管理系统v2/后台管理系统/ms/src/views/users/UserList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-list\">\r\n    <div class=\"page-header\">\r\n      <h2>用户管理</h2>\r\n      <el-button type=\"primary\" @click=\"showAddDialog\">\r\n        <el-icon><Plus /></el-icon>\r\n        添加用户\r\n      </el-button>\r\n    </div>\r\n\r\n    <div class=\"search-area\">\r\n      <el-card shadow=\"never\">\r\n        <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n          <el-form-item label=\"用户名\">\r\n            <el-input\r\n              v-model=\"searchForm.username\"\r\n              placeholder=\"请输入用户名\"\r\n              clearable\r\n              style=\"width: 180px\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"邮箱\">\r\n            <el-input\r\n              v-model=\"searchForm.email\"\r\n              placeholder=\"请输入邮箱\"\r\n              clearable\r\n              style=\"width: 200px\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"角色\">\r\n            <el-select\r\n              v-model=\"searchForm.role\"\r\n              placeholder=\"请选择角色\"\r\n              clearable\r\n              style=\"width: 120px\"\r\n            >\r\n              <el-option label=\"管理员\" value=\"admin\" />\r\n              <el-option label=\"用户\" value=\"user\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"fetchUsers\" :loading=\"loading\">\r\n              <el-icon><Search /></el-icon>\r\n              搜索\r\n            </el-button>\r\n            <el-button @click=\"resetSearch\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-card>\r\n    </div>\r\n\r\n    <div class=\"table-container\">\r\n      <el-card shadow=\"never\">\r\n        <div class=\"table-header\">\r\n          <div class=\"table-title\">\r\n            <span>用户列表</span>\r\n            <span class=\"count\">共 {{ users.length }} 条</span>\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          :data=\"users\"\r\n          v-loading=\"loading\"\r\n          stripe\r\n          border\r\n          empty-text=\"暂无用户数据\"\r\n          style=\"width: 100%\"\r\n        >\r\n          <el-table-column prop=\"id\" label=\"ID\" width=\"80\" align=\"center\" />\r\n          <el-table-column prop=\"username\" label=\"用户名\" width=\"120\" show-overflow-tooltip />\r\n          <el-table-column prop=\"password\" label=\"密码\" width=\"200\" show-overflow-tooltip>\r\n            <template #default=\"{ row }\">\r\n              <div class=\"password-cell\">\r\n                <span v-if=\"!row.showPassword\" class=\"password-mask\">\r\n                  {{ '●'.repeat(8) }}\r\n                </span>\r\n                <span v-else class=\"password-text\">{{ row.password }}</span>\r\n                <el-button\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                  @click=\"togglePassword(row)\"\r\n                  class=\"password-toggle\"\r\n                >\r\n                  <el-icon>\r\n                    <View v-if=\"!row.showPassword\" />\r\n                    <Hide v-else />\r\n                  </el-icon>\r\n                </el-button>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"email\" label=\"邮箱\"  show-overflow-tooltip />\r\n          <el-table-column prop=\"phone\" label=\"手机号\"  />\r\n          <el-table-column prop=\"role\" label=\"角色\" width=\"100\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <el-tag :type=\"getRoleType(row.role)\" size=\"small\">\r\n                {{ getRoleText(row.role) }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <el-tag :type=\"row.status === 1 ? 'success' : 'danger'\" size=\"small\">\r\n                {{ row.status === 1 ? '启用' : '禁用' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              {{ formatDate(row.created_at) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"220\" fixed=\"right\" align=\"center\">\r\n            <template #default=\"{ row }\">\r\n              <el-button type=\"primary\" size=\"small\" @click=\"viewUser(row)\" link>\r\n                <el-icon><View /></el-icon>\r\n                查看\r\n              </el-button>\r\n              <el-button type=\"warning\" size=\"small\" @click=\"editUser(row)\" link>\r\n                <el-icon><Edit /></el-icon>\r\n                编辑\r\n              </el-button>\r\n              <el-button type=\"success\" size=\"small\" @click=\"resetPassword(row)\" link>\r\n                <el-icon><Key /></el-icon>\r\n                重置密码\r\n              </el-button>\r\n              <el-button type=\"danger\" size=\"small\" @click=\"deleteUser(row)\" link>\r\n                <el-icon><Delete /></el-icon>\r\n                删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 用户详情对话框 -->\r\n    <el-dialog v-model=\"userDetailVisible\" title=\"用户详情\" width=\"600px\">\r\n      <div v-if=\"currentUser\" class=\"user-detail\">\r\n        <el-descriptions :column=\"2\" border>\r\n          <el-descriptions-item label=\"ID\">{{ currentUser.id }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"用户名\">{{ currentUser.username }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"邮箱\">{{ currentUser.email }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"手机号\">{{ currentUser.phone || '-' }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"角色\">\r\n            <el-tag :type=\"getRoleType(currentUser.role)\">\r\n              {{ getRoleText(currentUser.role) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"状态\">\r\n            <el-tag :type=\"currentUser.status === 1 ? 'success' : 'danger'\">\r\n              {{ currentUser.status === 1 ? '启用' : '禁用' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\" :span=\"2\">{{ formatDate(currentUser.created_at) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"最后登录\" :span=\"2\">{{ formatDate(currentUser.last_login) || '-' }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加/编辑用户对话框 -->\r\n    <el-dialog\r\n      :title=\"isEdit ? '编辑用户' : '添加用户'\"\r\n      v-model=\"dialogVisible\"\r\n      width=\"500px\"\r\n      @close=\"resetForm\"\r\n    >\r\n      <el-form\r\n        ref=\"formRef\"\r\n        :model=\"userForm\"\r\n        :rules=\"formRules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"userForm.username\" placeholder=\"请输入用户名\" :disabled=\"isEdit\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\" prop=\"password\" v-if=\"!isEdit\">\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password />\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\">\r\n          <el-input v-model=\"userForm.email\" placeholder=\"请输入邮箱\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"userForm.phone\" placeholder=\"请输入手机号\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"角色\" prop=\"role\">\r\n          <el-select v-model=\"userForm.role\" placeholder=\"请选择角色\" style=\"width: 100%\">\r\n            <el-option label=\"管理员\" value=\"admin\" />\r\n            <el-option label=\"用户\" value=\"user\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-radio-group v-model=\"userForm.status\">\r\n            <el-radio :label=\"1\">启用</el-radio>\r\n            <el-radio :label=\"0\">禁用</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">\r\n            {{ isEdit ? '更新' : '创建' }}\r\n          </el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 重置密码对话框 -->\r\n    <el-dialog v-model=\"resetPasswordVisible\" title=\"重置密码\" width=\"400px\">\r\n      <el-form :model=\"passwordForm\" label-width=\"80px\">\r\n        <el-form-item label=\"用户\">\r\n          <el-input :value=\"passwordForm.username\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"新密码\" required>\r\n          <el-input\r\n            v-model=\"passwordForm.newPassword\"\r\n            type=\"password\"\r\n            placeholder=\"请输入新密码\"\r\n            show-password\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"确认密码\" required>\r\n          <el-input\r\n            v-model=\"passwordForm.confirmPassword\"\r\n            type=\"password\"\r\n            placeholder=\"请再次输入新密码\"\r\n            show-password\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"resetPasswordVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"confirmResetPassword\" :loading=\"resetting\">确定</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Plus, Search, View, Hide, Edit, Delete, Key } from '@element-plus/icons-vue'\r\nimport { userAPI } from '@/utils/api'\r\n\r\nconst users = ref([])\r\nconst loading = ref(false)\r\nconst dialogVisible = ref(false)\r\nconst userDetailVisible = ref(false)\r\nconst resetPasswordVisible = ref(false)\r\nconst isEdit = ref(false)\r\nconst submitting = ref(false)\r\nconst resetting = ref(false)\r\nconst formRef = ref()\r\nconst currentUser = ref(null)\r\n\r\nconst searchForm = reactive({\r\n  username: '',\r\n  email: '',\r\n  role: ''\r\n})\r\n\r\nconst userForm = reactive({\r\n  id: null,\r\n  username: '',\r\n  password: '',\r\n  email: '',\r\n  phone: '',\r\n  role: 'user',\r\n  status: 1\r\n})\r\n\r\nconst passwordForm = reactive({\r\n  userId: null,\r\n  username: '',\r\n  newPassword: '',\r\n  confirmPassword: ''\r\n})\r\n\r\nconst formRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, message: '用户名长度不能少于3位', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\r\n  ],\r\n  role: [\r\n    { required: true, message: '请选择角色', trigger: 'change' }\r\n  ]\r\n}\r\n\r\nconst fetchUsers = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await userAPI.getAll(searchForm)\r\n    const userData = response.data.data || response.data\r\n    // 为每个用户添加密码显示状态\r\n    users.value = userData.map(user => ({\r\n      ...user,\r\n      showPassword: false\r\n    }))\r\n  } catch (error) {\r\n    ElMessage.error('获取用户列表失败')\r\n    console.error('获取用户失败:', error)\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\nconst formatDate = (date) => {\r\n  if (!date) return '-'\r\n  return new Date(date).toLocaleString('zh-CN', {\r\n    year: 'numeric',\r\n    month: '2-digit',\r\n    day: '2-digit',\r\n    hour: '2-digit',\r\n    minute: '2-digit'\r\n  })\r\n}\r\n\r\nconst getRoleType = (role) => {\r\n  return role === 'admin' ? 'danger' : 'primary'\r\n}\r\n\r\nconst getRoleText = (role) => {\r\n  return role === 'admin' ? '管理员' : '用户'\r\n}\r\n\r\nconst togglePassword = (row) => {\r\n  row.showPassword = !row.showPassword\r\n}\r\n\r\nconst resetSearch = () => {\r\n  searchForm.username = ''\r\n  searchForm.email = ''\r\n  searchForm.role = ''\r\n  fetchUsers()\r\n}\r\n\r\nconst viewUser = (row) => {\r\n  currentUser.value = row\r\n  userDetailVisible.value = true\r\n}\r\n\r\nconst showAddDialog = () => {\r\n  isEdit.value = false\r\n  dialogVisible.value = true\r\n}\r\n\r\nconst editUser = (row) => {\r\n  isEdit.value = true\r\n  Object.assign(userForm, {\r\n    ...row,\r\n    password: '' // 编辑时不显示密码\r\n  })\r\n  dialogVisible.value = true\r\n}\r\n\r\nconst resetForm = () => {\r\n  Object.assign(userForm, {\r\n    id: null,\r\n    username: '',\r\n    password: '',\r\n    email: '',\r\n    phone: '',\r\n    role: 'user',\r\n    status: 1\r\n  })\r\n  if (formRef.value) {\r\n    formRef.value.resetFields()\r\n  }\r\n}\r\n\r\nconst submitForm = async () => {\r\n  if (!formRef.value) return\r\n\r\n  try {\r\n    await formRef.value.validate()\r\n    submitting.value = true\r\n\r\n    if (isEdit.value) {\r\n      // 编辑时不传递密码字段\r\n      const { password, ...updateData } = userForm\r\n      await userAPI.update(userForm.id, updateData)\r\n      ElMessage.success('用户信息更新成功')\r\n    } else {\r\n      await userAPI.create(userForm)\r\n      ElMessage.success('用户创建成功')\r\n    }\r\n\r\n    dialogVisible.value = false\r\n    fetchUsers()\r\n  } catch (error) {\r\n    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')\r\n    console.error('提交表单失败:', error)\r\n  } finally {\r\n    submitting.value = false\r\n  }\r\n}\r\n\r\nconst resetPassword = (row) => {\r\n  passwordForm.userId = row.id\r\n  passwordForm.username = row.username\r\n  passwordForm.newPassword = ''\r\n  passwordForm.confirmPassword = ''\r\n  resetPasswordVisible.value = true\r\n}\r\n\r\nconst confirmResetPassword = async () => {\r\n  if (!passwordForm.newPassword) {\r\n    ElMessage.warning('请输入新密码')\r\n    return\r\n  }\r\n\r\n  if (passwordForm.newPassword.length < 6) {\r\n    ElMessage.warning('密码长度不能少于6位')\r\n    return\r\n  }\r\n\r\n  if (passwordForm.newPassword !== passwordForm.confirmPassword) {\r\n    ElMessage.warning('两次输入的密码不一致')\r\n    return\r\n  }\r\n\r\n  resetting.value = true\r\n  try {\r\n    await userAPI.resetPassword(passwordForm.userId, {\r\n      password: passwordForm.newPassword\r\n    })\r\n    ElMessage.success('密码重置成功')\r\n    resetPasswordVisible.value = false\r\n    fetchUsers()\r\n  } catch (error) {\r\n    ElMessage.error('密码重置失败')\r\n    console.error('重置密码失败:', error)\r\n  } finally {\r\n    resetting.value = false\r\n  }\r\n}\r\n\r\nconst deleteUser = async (row) => {\r\n  if (row.username === 'admin') {\r\n    ElMessage.warning('不能删除管理员账户')\r\n    return\r\n  }\r\n\r\n  try {\r\n    await ElMessageBox.confirm(\r\n      `确定要删除用户 \"${row.username}\" 吗？此操作不可恢复。`,\r\n      '删除确认',\r\n      {\r\n        confirmButtonText: '确定删除',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n        dangerouslyUseHTMLString: false\r\n      }\r\n    )\r\n\r\n    await userAPI.delete(row.id)\r\n    ElMessage.success('用户删除成功')\r\n    fetchUsers()\r\n  } catch (error) {\r\n    if (error !== 'cancel') {\r\n      ElMessage.error('删除失败')\r\n      console.error('删除用户失败:', error)\r\n    }\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  fetchUsers()\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.user-list {\r\n  padding: 20px;\r\n  background-color: #f5f7fa;\r\n  min-height: calc(100vh - 60px);\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.page-header h2 {\r\n  margin: 0;\r\n  color: #303133;\r\n  font-size: 20px;\r\n  font-weight: 500;\r\n}\r\n\r\n.search-area {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.search-form {\r\n  margin: 0;\r\n}\r\n\r\n.search-form .el-form-item {\r\n  margin-bottom: 0;\r\n  margin-right: 20px;\r\n}\r\n\r\n.table-container {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.table-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding: 0 4px;\r\n}\r\n\r\n.table-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.count {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  font-weight: normal;\r\n}\r\n\r\n.password-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.password-mask {\r\n  font-family: monospace;\r\n  color: #909399;\r\n  letter-spacing: 2px;\r\n}\r\n\r\n.password-text {\r\n  font-family: monospace;\r\n  color: #606266;\r\n  word-break: break-all;\r\n}\r\n\r\n.password-toggle {\r\n  padding: 4px;\r\n  min-height: auto;\r\n}\r\n\r\n.user-detail {\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 表格样式优化 */\r\n:deep(.el-table) {\r\n  font-size: 14px;\r\n}\r\n\r\n:deep(.el-table th) {\r\n  background-color: #fafafa;\r\n  color: #606266;\r\n  font-weight: 500;\r\n}\r\n\r\n:deep(.el-table td) {\r\n  padding: 12px 0;\r\n}\r\n\r\n:deep(.el-table .el-button + .el-button) {\r\n  margin-left: 8px;\r\n}\r\n\r\n/* 卡片样式 */\r\n:deep(.el-card) {\r\n  border: 1px solid #ebeef5;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);\r\n}\r\n\r\n:deep(.el-card__body) {\r\n  padding: 20px;\r\n}\r\n\r\n/* 对话框样式 */\r\n:deep(.el-dialog__header) {\r\n  padding: 20px 20px 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n:deep(.el-dialog__body) {\r\n  padding: 20px;\r\n}\r\n\r\n:deep(.el-descriptions) {\r\n  margin-top: 10px;\r\n}\r\n\r\n:deep(.el-descriptions__label) {\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n/* 表单样式 */\r\n:deep(.el-form-item__label) {\r\n  font-weight: 500;\r\n  color: #606266;\r\n}\r\n\r\n:deep(.el-radio-group) {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-list {\r\n    padding: 16px;\r\n  }\r\n\r\n  .search-form .el-form-item {\r\n    margin-right: 12px;\r\n    margin-bottom: 12px;\r\n  }\r\n\r\n  :deep(.el-table .el-button) {\r\n    padding: 4px 8px;\r\n    font-size: 12px;\r\n  }\r\n\r\n  .password-cell {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 4px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;AAmPA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAK;AAC9C,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,GAAG,QAAQ,yBAAyB;AACrF,SAASC,OAAO,QAAQ,aAAa;;;;;;;IAErC,MAAMC,KAAK,GAAGb,GAAG,CAAC,EAAE,CAAC;IACrB,MAAMc,OAAO,GAAGd,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAMe,aAAa,GAAGf,GAAG,CAAC,KAAK,CAAC;IAChC,MAAMgB,iBAAiB,GAAGhB,GAAG,CAAC,KAAK,CAAC;IACpC,MAAMiB,oBAAoB,GAAGjB,GAAG,CAAC,KAAK,CAAC;IACvC,MAAMkB,MAAM,GAAGlB,GAAG,CAAC,KAAK,CAAC;IACzB,MAAMmB,UAAU,GAAGnB,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMoB,SAAS,GAAGpB,GAAG,CAAC,KAAK,CAAC;IAC5B,MAAMqB,OAAO,GAAGrB,GAAG,CAAC,CAAC;IACrB,MAAMsB,WAAW,GAAGtB,GAAG,CAAC,IAAI,CAAC;IAE7B,MAAMuB,UAAU,GAAGtB,QAAQ,CAAC;MAC1BuB,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE;IACR,CAAC,CAAC;IAEF,MAAMC,QAAQ,GAAG1B,QAAQ,CAAC;MACxB2B,EAAE,EAAE,IAAI;MACRJ,QAAQ,EAAE,EAAE;MACZK,QAAQ,EAAE,EAAE;MACZJ,KAAK,EAAE,EAAE;MACTK,KAAK,EAAE,EAAE;MACTJ,IAAI,EAAE,MAAM;MACZK,MAAM,EAAE;IACV,CAAC,CAAC;IAEF,MAAMC,YAAY,GAAG/B,QAAQ,CAAC;MAC5BgC,MAAM,EAAE,IAAI;MACZT,QAAQ,EAAE,EAAE;MACZU,WAAW,EAAE,EAAE;MACfC,eAAe,EAAE;IACnB,CAAC,CAAC;IAEF,MAAMC,SAAS,GAAG;MAChBZ,QAAQ,EAAE,CACR;QAAEa,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEF,OAAO,EAAE,aAAa;QAAEC,OAAO,EAAE;MAAO,CAAC,CACpD;MACDV,QAAQ,EAAE,CACR;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEF,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CACnD;MACDd,KAAK,EAAE,CACL;QAAEY,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEE,IAAI,EAAE,OAAO;QAAEH,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CAC1D;MACDb,IAAI,EAAE,CACJ;QAAEW,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC;IAE3D,CAAC;IAED,MAAMG,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B5B,OAAO,CAAC6B,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMhC,OAAO,CAACiC,MAAM,CAACtB,UAAU,CAAC;QACjD,MAAMuB,QAAQ,GAAGF,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAIH,QAAQ,CAACG,IAAI;QACpD;QACAlC,KAAK,CAAC8B,KAAK,GAAGG,QAAQ,CAACE,GAAG,CAACC,IAAI,KAAK;UAClC,GAAGA,IAAI;UACPC,YAAY,EAAE;QAChB,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdhD,SAAS,CAACgD,KAAK,CAAC,UAAU,CAAC;QAC3BC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC,CAAC,SAAS;QACRrC,OAAO,CAAC6B,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;IAED,MAAMU,UAAU,GAAIC,IAAI,IAAK;MAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,GAAG;MACrB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;QAC5CC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;IAED,MAAMC,WAAW,GAAIpC,IAAI,IAAK;MAC5B,OAAOA,IAAI,KAAK,OAAO,GAAG,QAAQ,GAAG,SAAS;IAChD,CAAC;IAED,MAAMqC,WAAW,GAAIrC,IAAI,IAAK;MAC5B,OAAOA,IAAI,KAAK,OAAO,GAAG,KAAK,GAAG,IAAI;IACxC,CAAC;IAED,MAAMsC,cAAc,GAAIC,GAAG,IAAK;MAC9BA,GAAG,CAACf,YAAY,GAAG,CAACe,GAAG,CAACf,YAAY;IACtC,CAAC;IAED,MAAMgB,WAAW,GAAGA,CAAA,KAAM;MACxB3C,UAAU,CAACC,QAAQ,GAAG,EAAE;MACxBD,UAAU,CAACE,KAAK,GAAG,EAAE;MACrBF,UAAU,CAACG,IAAI,GAAG,EAAE;MACpBgB,UAAU,CAAC,CAAC;IACd,CAAC;IAED,MAAMyB,QAAQ,GAAIF,GAAG,IAAK;MACxB3C,WAAW,CAACqB,KAAK,GAAGsB,GAAG;MACvBjD,iBAAiB,CAAC2B,KAAK,GAAG,IAAI;IAChC,CAAC;IAED,MAAMyB,aAAa,GAAGA,CAAA,KAAM;MAC1BlD,MAAM,CAACyB,KAAK,GAAG,KAAK;MACpB5B,aAAa,CAAC4B,KAAK,GAAG,IAAI;IAC5B,CAAC;IAED,MAAM0B,QAAQ,GAAIJ,GAAG,IAAK;MACxB/C,MAAM,CAACyB,KAAK,GAAG,IAAI;MACnB2B,MAAM,CAACC,MAAM,CAAC5C,QAAQ,EAAE;QACtB,GAAGsC,GAAG;QACNpC,QAAQ,EAAE,EAAE,CAAC;MACf,CAAC,CAAC;MACFd,aAAa,CAAC4B,KAAK,GAAG,IAAI;IAC5B,CAAC;IAED,MAAM6B,SAAS,GAAGA,CAAA,KAAM;MACtBF,MAAM,CAACC,MAAM,CAAC5C,QAAQ,EAAE;QACtBC,EAAE,EAAE,IAAI;QACRJ,QAAQ,EAAE,EAAE;QACZK,QAAQ,EAAE,EAAE;QACZJ,KAAK,EAAE,EAAE;QACTK,KAAK,EAAE,EAAE;QACTJ,IAAI,EAAE,MAAM;QACZK,MAAM,EAAE;MACV,CAAC,CAAC;MACF,IAAIV,OAAO,CAACsB,KAAK,EAAE;QACjBtB,OAAO,CAACsB,KAAK,CAAC8B,WAAW,CAAC,CAAC;MAC7B;IACF,CAAC;IAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAACrD,OAAO,CAACsB,KAAK,EAAE;MAEpB,IAAI;QACF,MAAMtB,OAAO,CAACsB,KAAK,CAACgC,QAAQ,CAAC,CAAC;QAC9BxD,UAAU,CAACwB,KAAK,GAAG,IAAI;QAEvB,IAAIzB,MAAM,CAACyB,KAAK,EAAE;UAChB;UACA,MAAM;YAAEd,QAAQ;YAAE,GAAG+C;UAAW,CAAC,GAAGjD,QAAQ;UAC5C,MAAMf,OAAO,CAACiE,MAAM,CAAClD,QAAQ,CAACC,EAAE,EAAEgD,UAAU,CAAC;UAC7CzE,SAAS,CAAC2E,OAAO,CAAC,UAAU,CAAC;QAC/B,CAAC,MAAM;UACL,MAAMlE,OAAO,CAACmE,MAAM,CAACpD,QAAQ,CAAC;UAC9BxB,SAAS,CAAC2E,OAAO,CAAC,QAAQ,CAAC;QAC7B;QAEA/D,aAAa,CAAC4B,KAAK,GAAG,KAAK;QAC3BD,UAAU,CAAC,CAAC;MACd,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdhD,SAAS,CAACgD,KAAK,CAACjC,MAAM,CAACyB,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;QAC/CS,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC,CAAC,SAAS;QACRhC,UAAU,CAACwB,KAAK,GAAG,KAAK;MAC1B;IACF,CAAC;IAED,MAAMqC,aAAa,GAAIf,GAAG,IAAK;MAC7BjC,YAAY,CAACC,MAAM,GAAGgC,GAAG,CAACrC,EAAE;MAC5BI,YAAY,CAACR,QAAQ,GAAGyC,GAAG,CAACzC,QAAQ;MACpCQ,YAAY,CAACE,WAAW,GAAG,EAAE;MAC7BF,YAAY,CAACG,eAAe,GAAG,EAAE;MACjClB,oBAAoB,CAAC0B,KAAK,GAAG,IAAI;IACnC,CAAC;IAED,MAAMsC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI,CAACjD,YAAY,CAACE,WAAW,EAAE;QAC7B/B,SAAS,CAAC+E,OAAO,CAAC,QAAQ,CAAC;QAC3B;MACF;MAEA,IAAIlD,YAAY,CAACE,WAAW,CAACiD,MAAM,GAAG,CAAC,EAAE;QACvChF,SAAS,CAAC+E,OAAO,CAAC,YAAY,CAAC;QAC/B;MACF;MAEA,IAAIlD,YAAY,CAACE,WAAW,KAAKF,YAAY,CAACG,eAAe,EAAE;QAC7DhC,SAAS,CAAC+E,OAAO,CAAC,YAAY,CAAC;QAC/B;MACF;MAEA9D,SAAS,CAACuB,KAAK,GAAG,IAAI;MACtB,IAAI;QACF,MAAM/B,OAAO,CAACoE,aAAa,CAAChD,YAAY,CAACC,MAAM,EAAE;UAC/CJ,QAAQ,EAAEG,YAAY,CAACE;QACzB,CAAC,CAAC;QACF/B,SAAS,CAAC2E,OAAO,CAAC,QAAQ,CAAC;QAC3B7D,oBAAoB,CAAC0B,KAAK,GAAG,KAAK;QAClCD,UAAU,CAAC,CAAC;MACd,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdhD,SAAS,CAACgD,KAAK,CAAC,QAAQ,CAAC;QACzBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MACjC,CAAC,SAAS;QACR/B,SAAS,CAACuB,KAAK,GAAG,KAAK;MACzB;IACF,CAAC;IAED,MAAMyC,UAAU,GAAG,MAAOnB,GAAG,IAAK;MAChC,IAAIA,GAAG,CAACzC,QAAQ,KAAK,OAAO,EAAE;QAC5BrB,SAAS,CAAC+E,OAAO,CAAC,WAAW,CAAC;QAC9B;MACF;MAEA,IAAI;QACF,MAAM9E,YAAY,CAACiF,OAAO,CACxB,YAAYpB,GAAG,CAACzC,QAAQ,cAAc,EACtC,MAAM,EACN;UACE8D,iBAAiB,EAAE,MAAM;UACzBC,gBAAgB,EAAE,IAAI;UACtB9C,IAAI,EAAE,SAAS;UACf+C,wBAAwB,EAAE;QAC5B,CACF,CAAC;QAED,MAAM5E,OAAO,CAAC6E,MAAM,CAACxB,GAAG,CAACrC,EAAE,CAAC;QAC5BzB,SAAS,CAAC2E,OAAO,CAAC,QAAQ,CAAC;QAC3BpC,UAAU,CAAC,CAAC;MACd,CAAC,CAAC,OAAOS,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtBhD,SAAS,CAACgD,KAAK,CAAC,MAAM,CAAC;UACvBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QACjC;MACF;IACF,CAAC;IAEDjD,SAAS,CAAC,MAAM;MACdwC,UAAU,CAAC,CAAC;IACd,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}