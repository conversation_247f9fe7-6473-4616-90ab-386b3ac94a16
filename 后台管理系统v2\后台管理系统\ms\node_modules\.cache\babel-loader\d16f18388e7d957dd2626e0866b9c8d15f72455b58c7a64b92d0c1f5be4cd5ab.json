{"ast": null, "code": "import { ref, reactive, onMounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Plus, Search } from '@element-plus/icons-vue';\nimport { orderAPI } from '@/utils/api';\nexport default {\n  __name: 'OrderList',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const orders = ref([]);\n    const loading = ref(false);\n    const searchForm = reactive({\n      orderNumber: '',\n      status: ''\n    });\n    const fetchOrders = async () => {\n      loading.value = true;\n      try {\n        const response = await orderAPI.getAll(searchForm);\n        orders.value = response.data.data || response.data;\n      } catch (error) {\n        ElMessage.error('获取订单列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n    const getStatusType = status => {\n      const types = {\n        pending: 'warning',\n        processing: 'primary',\n        completed: 'success',\n        cancelled: 'danger'\n      };\n      return types[status] || 'info';\n    };\n    const getStatusText = status => {\n      const texts = {\n        pending: '待处理',\n        processing: '处理中',\n        completed: '已完成',\n        cancelled: '已取消'\n      };\n      return texts[status] || status;\n    };\n    const formatDate = date => {\n      return new Date(date).toLocaleString();\n    };\n    const resetSearch = () => {\n      searchForm.orderNumber = '';\n      searchForm.status = '';\n      fetchOrders();\n    };\n    const showAddDialog = () => {\n      // 实现添加订单逻辑\n      ElMessage.info('添加订单功能待实现');\n    };\n    const editOrder = row => {\n      // 实现编辑订单逻辑\n      ElMessage.info('编辑订单功能待实现');\n    };\n    const updateStatus = async row => {\n      // 实现更新状态逻辑\n      ElMessage.info('更新状态功能待实现');\n    };\n    const deleteOrder = async row => {\n      try {\n        await ElMessageBox.confirm('确定要删除这个订单吗？', '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        await orderAPI.delete(row.id);\n        ElMessage.success('删除成功');\n        fetchOrders();\n      } catch (error) {\n        if (error !== 'cancel') {\n          ElMessage.error('删除失败');\n        }\n      }\n    };\n    onMounted(() => {\n      fetchOrders();\n    });\n    const __returned__ = {\n      orders,\n      loading,\n      searchForm,\n      fetchOrders,\n      getStatusType,\n      getStatusText,\n      formatDate,\n      resetSearch,\n      showAddDialog,\n      editOrder,\n      updateStatus,\n      deleteOrder,\n      ref,\n      reactive,\n      onMounted,\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get Plus() {\n        return Plus;\n      },\n      get Search() {\n        return Search;\n      },\n      get orderAPI() {\n        return orderAPI;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "ElMessage", "ElMessageBox", "Plus", "Search", "orderAPI", "orders", "loading", "searchForm", "orderNumber", "status", "fetchOrders", "value", "response", "getAll", "data", "error", "getStatusType", "types", "pending", "processing", "completed", "cancelled", "getStatusText", "texts", "formatDate", "date", "Date", "toLocaleString", "resetSearch", "showAddDialog", "info", "editOrder", "row", "updateStatus", "deleteOrder", "confirm", "confirmButtonText", "cancelButtonText", "type", "delete", "id", "success"], "sources": ["D:/admin/202506/乐高/乐高后台/后台管理系统v2/后台管理系统/ms/src/views/orders/OrderList.vue"], "sourcesContent": ["<template>\n  <div class=\"order-list\">\n    <div class=\"page-header\">\n      <h2>订单管理</h2>\n    </div>\n\n    <div class=\"search-area\">\n      <el-card shadow=\"never\">\n        <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\n          <el-form-item label=\"订单号\">\n            <el-input\n              v-model=\"searchForm.order_number\"\n              placeholder=\"请输入订单号\"\n              clearable\n              style=\"width: 200px\"\n            />\n          </el-form-item>\n          <el-form-item label=\"客户姓名\">\n            <el-input\n              v-model=\"searchForm.name\"\n              placeholder=\"请输入客户姓名\"\n              clearable\n              style=\"width: 150px\"\n            />\n          </el-form-item>\n          <el-form-item label=\"状态\">\n            <el-select v-model=\"searchForm.status\" placeholder=\"请选择状态\" clearable style=\"width: 120px\">\n              <el-option label=\"待处理\" value=\"pending\" />\n              <el-option label=\"处理中\" value=\"processing\" />\n              <el-option label=\"已完成\" value=\"completed\" />\n              <el-option label=\"已取消\" value=\"cancelled\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"fetchOrders\" :loading=\"loading\">\n              <el-icon><Search /></el-icon>\n              搜索\n            </el-button>\n            <el-button @click=\"resetSearch\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-card>\n    </div>\n\n    <div class=\"table-container\">\n      <el-card shadow=\"never\">\n        <div class=\"table-header\">\n          <div class=\"table-title\">\n            <span>订单列表</span>\n            <span class=\"count\">共 {{ orders.length }} 条</span>\n          </div>\n        </div>\n\n        <el-table\n          :data=\"orders\"\n          v-loading=\"loading\"\n          stripe\n          border\n          empty-text=\"暂无订单数据\"\n          style=\"width: 100%\"\n        >\n          <el-table-column prop=\"id\" label=\"ID\" width=\"80\" align=\"center\" />\n          <el-table-column prop=\"order_number\" label=\"订单号\" width=\"180\" show-overflow-tooltip />\n          <el-table-column prop=\"name\" label=\"客户姓名\" width=\"120\" show-overflow-tooltip />\n          <el-table-column prop=\"email\" label=\"邮箱\" width=\"180\" show-overflow-tooltip />\n          <el-table-column prop=\"phone\" label=\"电话\" width=\"130\" />\n          <el-table-column prop=\"order_items\" label=\"订单内容\" min-width=\"200\" show-overflow-tooltip>\n            <template #default=\"{ row }\">\n              <div class=\"order-items\">\n                {{ formatOrderItems(row.order_items) }}\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"status\" label=\"状态\" width=\"100\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-tag :type=\"getStatusType(row.status)\" size=\"small\">\n                {{ getStatusText(row.status) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\" align=\"center\">\n            <template #default=\"{ row }\">\n              {{ formatDate(row.created_at) }}\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"200\" fixed=\"right\" align=\"center\">\n            <template #default=\"{ row }\">\n              <el-button type=\"primary\" size=\"small\" @click=\"viewOrder(row)\" link>\n                <el-icon><View /></el-icon>\n                查看\n              </el-button>\n              <el-button type=\"warning\" size=\"small\" @click=\"updateStatus(row)\" link>\n                <el-icon><Edit /></el-icon>\n                状态\n              </el-button>\n              <el-button type=\"danger\" size=\"small\" @click=\"deleteOrder(row)\" link>\n                <el-icon><Delete /></el-icon>\n                删除\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-card>\n    </div>\n\n    <!-- 订单详情对话框 -->\n    <el-dialog v-model=\"orderDetailVisible\" title=\"订单详情\" width=\"600px\">\n      <div v-if=\"currentOrder\" class=\"order-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"订单号\">{{ currentOrder.order_number }}</el-descriptions-item>\n          <el-descriptions-item label=\"状态\">\n            <el-tag :type=\"getStatusType(currentOrder.status)\">\n              {{ getStatusText(currentOrder.status) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"客户姓名\">{{ currentOrder.name }}</el-descriptions-item>\n          <el-descriptions-item label=\"电话\">{{ currentOrder.phone }}</el-descriptions-item>\n          <el-descriptions-item label=\"邮箱\" :span=\"2\">{{ currentOrder.email }}</el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\" :span=\"2\">{{ formatDate(currentOrder.created_at) }}</el-descriptions-item>\n          <el-descriptions-item label=\"订单内容\" :span=\"2\">\n            <div class=\"order-content\">\n              {{ formatOrderItems(currentOrder.order_items) }}\n            </div>\n          </el-descriptions-item>\n        </el-descriptions>\n      </div>\n    </el-dialog>\n\n    <!-- 更新状态对话框 -->\n    <el-dialog v-model=\"statusUpdateVisible\" title=\"更新订单状态\" width=\"400px\">\n      <el-form :model=\"statusForm\" label-width=\"80px\">\n        <el-form-item label=\"当前状态\">\n          <el-tag :type=\"getStatusType(statusForm.currentStatus)\">\n            {{ getStatusText(statusForm.currentStatus) }}\n          </el-tag>\n        </el-form-item>\n        <el-form-item label=\"新状态\" required>\n          <el-select v-model=\"statusForm.newStatus\" placeholder=\"请选择新状态\" style=\"width: 100%\">\n            <el-option label=\"待处理\" value=\"pending\" />\n            <el-option label=\"处理中\" value=\"processing\" />\n            <el-option label=\"已完成\" value=\"completed\" />\n            <el-option label=\"已取消\" value=\"cancelled\" />\n          </el-select>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"statusUpdateVisible = false\">取消</el-button>\n          <el-button type=\"primary\" @click=\"confirmUpdateStatus\" :loading=\"updating\">确定</el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { ref, reactive, onMounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Plus, Search } from '@element-plus/icons-vue'\nimport { orderAPI } from '@/utils/api'\n\nconst orders = ref([])\nconst loading = ref(false)\n\nconst searchForm = reactive({\n  orderNumber: '',\n  status: ''\n})\n\nconst fetchOrders = async () => {\n  loading.value = true\n  try {\n    const response = await orderAPI.getAll(searchForm)\n    orders.value = response.data.data || response.data\n  } catch (error) {\n    ElMessage.error('获取订单列表失败')\n  } finally {\n    loading.value = false\n  }\n}\n\nconst getStatusType = (status) => {\n  const types = {\n    pending: 'warning',\n    processing: 'primary',\n    completed: 'success',\n    cancelled: 'danger'\n  }\n  return types[status] || 'info'\n}\n\nconst getStatusText = (status) => {\n  const texts = {\n    pending: '待处理',\n    processing: '处理中',\n    completed: '已完成',\n    cancelled: '已取消'\n  }\n  return texts[status] || status\n}\n\nconst formatDate = (date) => {\n  return new Date(date).toLocaleString()\n}\n\nconst resetSearch = () => {\n  searchForm.orderNumber = ''\n  searchForm.status = ''\n  fetchOrders()\n}\n\nconst showAddDialog = () => {\n  // 实现添加订单逻辑\n  ElMessage.info('添加订单功能待实现')\n}\n\nconst editOrder = (row) => {\n  // 实现编辑订单逻辑\n  ElMessage.info('编辑订单功能待实现')\n}\n\nconst updateStatus = async (row) => {\n  // 实现更新状态逻辑\n  ElMessage.info('更新状态功能待实现')\n}\n\nconst deleteOrder = async (row) => {\n  try {\n    await ElMessageBox.confirm('确定要删除这个订单吗？', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    })\n    \n    await orderAPI.delete(row.id)\n    ElMessage.success('删除成功')\n    fetchOrders()\n  } catch (error) {\n    if (error !== 'cancel') {\n      ElMessage.error('删除失败')\n    }\n  }\n}\n\nonMounted(() => {\n  fetchOrders()\n})\n</script>\n\n<style scoped>\n.order-list {\n  padding: 24px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding-bottom: 16px;\n  border-bottom: 1px solid #e8eaec;\n}\n\n.search-area {\n  background-color: #fafafa;\n  padding: 16px;\n  border-radius: 6px;\n  margin-bottom: 16px;\n}\n\n.table-container {\n  background-color: #fff;\n  border-radius: 6px;\n  overflow: hidden;\n}\n</style>\n"], "mappings": "AA4JA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,KAAI;AAC7C,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAa;AACrD,SAASC,IAAI,EAAEC,MAAM,QAAQ,yBAAwB;AACrD,SAASC,QAAQ,QAAQ,aAAY;;;;;;;IAErC,MAAMC,MAAM,GAAGR,GAAG,CAAC,EAAE;IACrB,MAAMS,OAAO,GAAGT,GAAG,CAAC,KAAK;IAEzB,MAAMU,UAAU,GAAGT,QAAQ,CAAC;MAC1BU,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE;IACV,CAAC;IAED,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9BJ,OAAO,CAACK,KAAK,GAAG,IAAG;MACnB,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMR,QAAQ,CAACS,MAAM,CAACN,UAAU;QACjDF,MAAM,CAACM,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAG;MACnD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdf,SAAS,CAACe,KAAK,CAAC,UAAU;MAC5B,CAAC,SAAS;QACRT,OAAO,CAACK,KAAK,GAAG,KAAI;MACtB;IACF;IAEA,MAAMK,aAAa,GAAIP,MAAM,IAAK;MAChC,MAAMQ,KAAK,GAAG;QACZC,OAAO,EAAE,SAAS;QAClBC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,SAAS;QACpBC,SAAS,EAAE;MACb;MACA,OAAOJ,KAAK,CAACR,MAAM,CAAC,IAAI,MAAK;IAC/B;IAEA,MAAMa,aAAa,GAAIb,MAAM,IAAK;MAChC,MAAMc,KAAK,GAAG;QACZL,OAAO,EAAE,KAAK;QACdC,UAAU,EAAE,KAAK;QACjBC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE;MACb;MACA,OAAOE,KAAK,CAACd,MAAM,CAAC,IAAIA,MAAK;IAC/B;IAEA,MAAMe,UAAU,GAAIC,IAAI,IAAK;MAC3B,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,CAAC;IACvC;IAEA,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxBrB,UAAU,CAACC,WAAW,GAAG,EAAC;MAC1BD,UAAU,CAACE,MAAM,GAAG,EAAC;MACrBC,WAAW,CAAC;IACd;IAEA,MAAMmB,aAAa,GAAGA,CAAA,KAAM;MAC1B;MACA7B,SAAS,CAAC8B,IAAI,CAAC,WAAW;IAC5B;IAEA,MAAMC,SAAS,GAAIC,GAAG,IAAK;MACzB;MACAhC,SAAS,CAAC8B,IAAI,CAAC,WAAW;IAC5B;IAEA,MAAMG,YAAY,GAAG,MAAOD,GAAG,IAAK;MAClC;MACAhC,SAAS,CAAC8B,IAAI,CAAC,WAAW;IAC5B;IAEA,MAAMI,WAAW,GAAG,MAAOF,GAAG,IAAK;MACjC,IAAI;QACF,MAAM/B,YAAY,CAACkC,OAAO,CAAC,aAAa,EAAE,IAAI,EAAE;UAC9CC,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CAAC;QAED,MAAMlC,QAAQ,CAACmC,MAAM,CAACP,GAAG,CAACQ,EAAE;QAC5BxC,SAAS,CAACyC,OAAO,CAAC,MAAM;QACxB/B,WAAW,CAAC;MACd,CAAC,CAAC,OAAOK,KAAK,EAAE;QACd,IAAIA,KAAK,KAAK,QAAQ,EAAE;UACtBf,SAAS,CAACe,KAAK,CAAC,MAAM;QACxB;MACF;IACF;IAEAhB,SAAS,CAAC,MAAM;MACdW,WAAW,CAAC;IACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}