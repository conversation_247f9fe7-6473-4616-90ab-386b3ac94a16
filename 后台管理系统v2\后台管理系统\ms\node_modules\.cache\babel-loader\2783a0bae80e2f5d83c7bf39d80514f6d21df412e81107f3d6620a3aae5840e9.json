{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, computed } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { User, Menu, Grid, Document, DataBoard, Setting, CaretBottom, Fold, Expand } from '@element-plus/icons-vue';\nexport default {\n  __name: 'AppLayout',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n    const route = useRoute();\n    const isCollapse = ref(false);\n    const username = ref(localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).username : '管理员');\n    const activeMenu = computed(() => {\n      return route.path;\n    });\n    const currentRoute = computed(() => {\n      return route.meta.title || '仪表盘';\n    });\n    const toggleSidebar = () => {\n      isCollapse.value = !isCollapse.value;\n    };\n    const handleProfile = () => {\n      ElMessage.info('个人中心功能开发中...');\n    };\n    const handleLogout = () => {\n      ElMessageBox.confirm('确定要退出登录吗?', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        localStorage.removeItem('token');\n        localStorage.removeItem('userInfo');\n        router.push('/login');\n        ElMessage.success('已退出登录');\n      }).catch(() => {});\n    };\n    const __returned__ = {\n      router,\n      route,\n      isCollapse,\n      username,\n      activeMenu,\n      currentRoute,\n      toggleSidebar,\n      handleProfile,\n      handleLogout,\n      ref,\n      computed,\n      get useRouter() {\n        return useRouter;\n      },\n      get useRoute() {\n        return useRoute;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get User() {\n        return User;\n      },\n      get Menu() {\n        return Menu;\n      },\n      get Grid() {\n        return Grid;\n      },\n      get Document() {\n        return Document;\n      },\n      get DataBoard() {\n        return DataBoard;\n      },\n      get Setting() {\n        return Setting;\n      },\n      get CaretBottom() {\n        return CaretBottom;\n      },\n      get Fold() {\n        return Fold;\n      },\n      get Expand() {\n        return Expand;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "useRouter", "useRoute", "ElMessage", "ElMessageBox", "User", "<PERSON><PERSON>", "Grid", "Document", "DataBoard", "Setting", "CaretBottom", "Fold", "Expand", "router", "route", "isCollapse", "username", "localStorage", "getItem", "JSON", "parse", "activeMenu", "path", "currentRoute", "meta", "title", "toggleSidebar", "value", "handleProfile", "info", "handleLogout", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "removeItem", "push", "success", "catch"], "sources": ["D:/admin/202506/乐高/乐高后台/后台管理系统v2/后台管理系统/ms/src/layout/AppLayout.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-container class=\"layout-container\">\r\n      <!-- 左侧菜单 -->\r\n      <el-aside :width=\"isCollapse ? '64px' : '220px'\" class=\"aside\">\r\n        <div class=\"logo\">\r\n          <img src=\"../assets/logo.png\" alt=\"logo\" v-if=\"!isCollapse\" />\r\n          <h1 v-show=\"!isCollapse\">后台管理系统</h1>\r\n          <el-icon v-if=\"isCollapse\" size=\"30\" color=\"#fff\"><Setting /></el-icon>\r\n        </div>\r\n        <el-scrollbar>\r\n          <el-menu\r\n            :default-active=\"activeMenu\"\r\n            class=\"el-menu-vertical\"\r\n            :collapse=\"isCollapse\"\r\n            background-color=\"#304156\"\r\n            text-color=\"#bfcbd9\"\r\n            active-text-color=\"#409EFF\"\r\n            router\r\n            :collapse-transition=\"false\"\r\n          >\r\n         \r\n            \r\n            <el-menu-item index=\"/categories\">\r\n              <el-icon><Menu /></el-icon>\r\n              <template #title>分类管理</template>\r\n            </el-menu-item>\r\n            \r\n            <el-menu-item index=\"/orders\">\r\n              <el-icon><Document /></el-icon>\r\n              <template #title>订单管理</template>\r\n            </el-menu-item>\r\n            \r\n            <el-menu-item index=\"/users\">\r\n              <el-icon><User /></el-icon>\r\n              <template #title>用户管理</template>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </el-scrollbar>\r\n      </el-aside>\r\n      \r\n      <!-- 右侧内容 -->\r\n      <el-container class=\"main-container\">\r\n        <!-- 顶部导航 -->\r\n        <el-header class=\"header\">\r\n          <div class=\"header-left\">\r\n            <el-icon class=\"fold-icon\" @click=\"toggleSidebar\">\r\n              <component :is=\"isCollapse ? 'Expand' : 'Fold'\"></component>\r\n            </el-icon>\r\n            <el-breadcrumb separator=\"/\" class=\"breadcrumb\">\r\n              <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n              <el-breadcrumb-item>{{ currentRoute }}</el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n          </div>\r\n          <div class=\"header-right\">\r\n            <el-dropdown trigger=\"click\">\r\n              <div class=\"user-info\">\r\n                <el-avatar :size=\"30\" src=\"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"></el-avatar>\r\n                <span>{{ username }}</span>\r\n                <el-icon><CaretBottom /></el-icon>\r\n              </div>\r\n              <template #dropdown>\r\n                <el-dropdown-menu>\r\n                  <el-dropdown-item @click=\"handleProfile\">个人中心</el-dropdown-item>\r\n                  <el-dropdown-item divided @click=\"handleLogout\">退出登录</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </template>\r\n            </el-dropdown>\r\n          </div>\r\n        </el-header>\r\n        \r\n        <!-- 内容区域 -->\r\n        <el-main class=\"main\">\r\n          <div class=\"content-wrapper\">\r\n            <router-view />\r\n          </div>\r\n        </el-main>\r\n      </el-container>\r\n    </el-container>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { \r\n  User,\r\n  Menu,\r\n  Grid,\r\n  Document,\r\n  DataBoard,\r\n  Setting,\r\n  CaretBottom, \r\n  Fold, \r\n  Expand \r\n} from '@element-plus/icons-vue'\r\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\nconst isCollapse = ref(false)\r\nconst username = ref(localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).username : '管理员')\r\n\r\nconst activeMenu = computed(() => {\r\n  return route.path\r\n})\r\n\r\nconst currentRoute = computed(() => {\r\n  return route.meta.title || '仪表盘'\r\n})\r\n\r\nconst toggleSidebar = () => {\r\n  isCollapse.value = !isCollapse.value\r\n}\r\n\r\nconst handleProfile = () => {\r\n  ElMessage.info('个人中心功能开发中...')\r\n}\r\n\r\nconst handleLogout = () => {\r\n  ElMessageBox.confirm('确定要退出登录吗?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => {\r\n    localStorage.removeItem('token')\r\n    localStorage.removeItem('userInfo')\r\n    router.push('/login')\r\n    ElMessage.success('已退出登录')\r\n  }).catch(() => {})\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n}\r\n\r\n.layout-container {\r\n  height: 100%;\r\n}\r\n\r\n.aside {\r\n  background-color: #304156;\r\n  transition: width 0.3s;\r\n  overflow: hidden;\r\n}\r\n\r\n.logo {\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #2b3649;\r\n  color: #fff;\r\n  padding: 0 15px;\r\n}\r\n\r\n.logo img {\r\n  width: 30px;\r\n  height: 30px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo h1 {\r\n  margin: 0;\r\n  color: #fff;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n.el-menu-vertical:not(.el-menu--collapse) {\r\n  width: 220px;\r\n}\r\n\r\n.main-container {\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n.header {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #e8eaec;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.fold-icon {\r\n  font-size: 18px;\r\n  cursor: pointer;\r\n  margin-right: 20px;\r\n  color: #666;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.fold-icon:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n.breadcrumb {\r\n  font-size: 14px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  padding: 5px 10px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.user-info:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.user-info span {\r\n  margin: 0 8px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.main {\r\n  padding: 20px;\r\n  background-color: #f0f2f5;\r\n  min-height: calc(100vh - 60px);\r\n}\r\n\r\n.content-wrapper {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  min-height: calc(100vh - 100px);\r\n}\r\n</style> \r\n"], "mappings": ";AAmFA,SAASA,GAAG,EAAEC,QAAQ,QAAQ,KAAK;AACnC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,YAAY;AAChD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SACEC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,SAAS,EACTC,OAAO,EACPC,WAAW,EACXC,IAAI,EACJC,MAAM,QACD,yBAAyB;;;;;;;IAEhC,MAAMC,MAAM,GAAGb,SAAS,CAAC,CAAC;IAC1B,MAAMc,KAAK,GAAGb,QAAQ,CAAC,CAAC;IACxB,MAAMc,UAAU,GAAGjB,GAAG,CAAC,KAAK,CAAC;IAC7B,MAAMkB,QAAQ,GAAGlB,GAAG,CAACmB,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,CAACF,QAAQ,GAAG,KAAK,CAAC;IAEtH,MAAMK,UAAU,GAAGtB,QAAQ,CAAC,MAAM;MAChC,OAAOe,KAAK,CAACQ,IAAI;IACnB,CAAC,CAAC;IAEF,MAAMC,YAAY,GAAGxB,QAAQ,CAAC,MAAM;MAClC,OAAOe,KAAK,CAACU,IAAI,CAACC,KAAK,IAAI,KAAK;IAClC,CAAC,CAAC;IAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1BX,UAAU,CAACY,KAAK,GAAG,CAACZ,UAAU,CAACY,KAAK;IACtC,CAAC;IAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B1B,SAAS,CAAC2B,IAAI,CAAC,cAAc,CAAC;IAChC,CAAC;IAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzB3B,YAAY,CAAC4B,OAAO,CAAC,WAAW,EAAE,IAAI,EAAE;QACtCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACZlB,YAAY,CAACmB,UAAU,CAAC,OAAO,CAAC;QAChCnB,YAAY,CAACmB,UAAU,CAAC,UAAU,CAAC;QACnCvB,MAAM,CAACwB,IAAI,CAAC,QAAQ,CAAC;QACrBnC,SAAS,CAACoC,OAAO,CAAC,OAAO,CAAC;MAC5B,CAAC,CAAC,CAACC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}