<template>
  <div class="app-container">
    <el-container class="layout-container">
      <!-- 左侧菜单 -->
      <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
        <div class="logo">
          <img src="../assets/logo.png" alt="logo" v-if="!isCollapse" />
          <h1 v-show="!isCollapse">后台管理系统</h1>
          <el-icon v-if="isCollapse" size="30" color="#fff"><Setting /></el-icon>
        </div>
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical"
            :collapse="isCollapse"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
            router
            :collapse-transition="false"
          >
         
            
            <el-menu-item index="/categories">
              <el-icon><Menu /></el-icon>
              <template #title>分类管理</template>
            </el-menu-item>
            
            <el-menu-item index="/orders">
              <el-icon><Document /></el-icon>
              <template #title>订单管理</template>
            </el-menu-item>
            
            <el-menu-item index="/users">
              <el-icon><User /></el-icon>
              <template #title>用户管理</template>
            </el-menu-item>
          </el-menu>
        </el-scrollbar>
      </el-aside>
      
      <!-- 右侧内容 -->
      <el-container class="main-container">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-icon class="fold-icon" @click="toggleSidebar">
              <component :is="isCollapse ? 'Expand' : 'Fold'"></component>
            </el-icon>
            <el-breadcrumb separator="/" class="breadcrumb">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentRoute }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
            <el-dropdown trigger="click">
              <div class="user-info">
                <el-avatar :size="30" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
                <span>{{ username }}</span>
                <el-icon><CaretBottom /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleProfile">个人中心</el-dropdown-item>
                  <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main">
          <div class="content-wrapper">
            <router-view />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User,
  Menu,
  Grid,
  Document,
  DataBoard,
  Setting,
  CaretBottom, 
  Fold, 
  Expand 
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const isCollapse = ref(false)
const username = ref(localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).username : '管理员')

const activeMenu = computed(() => {
  return route.path
})

const currentRoute = computed(() => {
  return route.meta.title || '仪表盘'
})

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const handleProfile = () => {
  ElMessage.info('个人中心功能开发中...')
}

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    router.push('/login')
    ElMessage.success('已退出登录')
  }).catch(() => {})
}
</script>

<style scoped>
.app-container {
  height: 100vh;
  width: 100%;
}

.layout-container {
  height: 100%;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3649;
  color: #fff;
  padding: 0 15px;
}

.logo img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.logo h1 {
  margin: 0;
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  white-space: nowrap;
}

.el-menu-vertical {
  border-right: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 220px;
}

.main-container {
  background-color: #f0f2f5;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e8eaec;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.fold-icon {
  font-size: 18px;
  cursor: pointer;
  margin-right: 20px;
  color: #666;
  transition: color 0.3s;
}

.fold-icon:hover {
  color: #409EFF;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.user-info span {
  margin: 0 8px;
  font-size: 14px;
  color: #333;
}

.main {
  padding: 20px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 60px);
}

.content-wrapper {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  min-height: calc(100vh - 100px);
}
</style> 
