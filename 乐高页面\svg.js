const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');
const { createCanvas, loadImage } = require('canvas');
const potrace = require('potrace');
const svgson = require('svgson');
const bounds = require('svg-path-bounds');

const app = express();
const port = 3001;

// 跨域处理中间件
app.use((req, res, next) => {
  // 允许所有来源访问，生产环境建议指定具体域名
  res.header('Access-Control-Allow-Origin', '*');

  // 允许的请求头
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  // 允许的请求方法
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');

  // 允许携带凭证
  res.header('Access-Control-Allow-Credentials', 'true');

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// 添加中间件解析JSON
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 设置静态文件服务
app.use(express.static('.'));

const OUTPUT_WIDTH = 692;
const OUTPUT_HEIGHT = 594;
const SCALE_FACTOR = 1;

// 设置上传目录
const UPLOAD_DIR = './uploads';
if (!fs.existsSync(UPLOAD_DIR)) fs.mkdirSync(UPLOAD_DIR);

// multer 配置用于处理上传文件
const storage = multer.diskStorage({
  destination: (req, file, cb) => cb(null, UPLOAD_DIR),
  filename: (req, file, cb) => cb(null, Date.now() + path.extname(file.originalname)),
});
const upload = multer({ storage });

// 路由：渲染主页面
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'template/index.html'));
});

// 路由：渲染乐高人仔定制页面
app.get('/lego', (req, res) => {
  res.sendFile(path.join(__dirname, '1(1).html'));
});

// 接口：上传图像，返回 base64 高清裁剪结果
app.post('/crop-image', upload.single('image'), async (req, res) => {
  const inputPath = req.file.path;

  try {
    // 生成时间戳用于文件命名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputFileName = `cropped_${timestamp}.png`;

    const base64Data = await new Promise((resolve, reject) => {
      potrace.trace(inputPath, async (err, svg) => {
        if (err) return reject(err);

        const parsed = await svgson.parse(svg);
        const pathNode = parsed.children.find(el => el.name === 'path');
        if (!pathNode || !pathNode.attributes.d) {
          return reject(new Error('未找到 SVG 路径数据'));
        }

        let [minX, minY, maxX, maxY] = bounds(pathNode.attributes.d);
        let cropWidth = maxX - minX;
        let cropHeight = maxY - minY;
        const targetRatio = OUTPUT_WIDTH / OUTPUT_HEIGHT;
        const cropRatio = cropWidth / cropHeight;

        if (cropRatio > targetRatio) {
          const newHeight = cropWidth / targetRatio;
          const heightDiff = newHeight - cropHeight;
          minY -= heightDiff / 2;
          maxY += heightDiff / 2;
          cropHeight = newHeight;
        } else {
          const newWidth = cropHeight * targetRatio;
          const widthDiff = newWidth - cropWidth;
          minX -= widthDiff / 2;
          maxX += widthDiff / 2;
          cropWidth = newWidth;
        }

        const cropX = Math.floor(minX);
        const cropY = Math.floor(minY);
        const cropW = Math.ceil(cropWidth);
        const cropH = Math.ceil(cropHeight);

        const img = await loadImage(inputPath);
        const canvas = createCanvas(OUTPUT_WIDTH * SCALE_FACTOR, OUTPUT_HEIGHT * SCALE_FACTOR);
        const ctx = canvas.getContext('2d');
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';
        ctx.scale(SCALE_FACTOR, SCALE_FACTOR);

        ctx.drawImage(img, cropX, cropY, cropW, cropH, 0, 0, OUTPUT_WIDTH, OUTPUT_HEIGHT);

        const buffer = canvas.toBuffer('image/png');
        const base64 = buffer.toString('base64');

        // // 保存本地图片
        // const outputPath = path.join(__dirname, 'output', outputFileName);

        // // 确保输出目录存在
        // const outputDir = path.dirname(outputPath);
        // if (!fs.existsSync(outputDir)) {
        //   fs.mkdirSync(outputDir, { recursive: true });
        // }

        // // 保存图片文件
        // fs.writeFileSync(outputPath, buffer);
        // console.log(`[本地保存] 图片已保存到: ${outputPath}`);

        resolve(base64);
      });
    });

    res.json({
      success: true,
      dataUrl: `data:image/png;base64,${base64Data}`,
      savedFile: outputFileName,
      message: `图片已处理并保存为 ${outputFileName}`
    });
  } catch (error) {
    console.error('[图像处理错误]', error);
    res.status(500).json({ success: false, error: '图像处理失败', detail: error.message });
  } finally {
    fs.unlink(inputPath, () => {}); // 删除临时上传文件
  }
});

// 启动服务
app.listen(port, () => {
  console.log(`✅ 服务运行中：http://localhost:${port}`);
});