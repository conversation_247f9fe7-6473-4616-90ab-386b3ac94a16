const { pool } = require('../config/db');
const bcrypt = require('bcryptjs');

class User {
  // 创建新用户
  static async create(userData) {
    try {
      // 加密密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);
      
      const [result] = await pool.query(
        `INSERT INTO users (username, password, email, phone, role, status, avatar) 
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          userData.username,
          hashedPassword,
          userData.email,
          userData.phone,
          userData.role || 'user',
          userData.status !== undefined ? userData.status : 1,
          userData.avatar
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }

  // 获取所有用户
  static async findAll(filters = {}) {
    try {
      let query = 'SELECT id, username, password, email, phone, role, status, avatar, last_login, created_at FROM users WHERE 1=1';
      const queryParams = [];
      
      // 添加过滤条件
      if (filters.username) {
        query += ' AND username LIKE ?';
        queryParams.push(`%${filters.username}%`);
      }
      
      if (filters.email) {
        query += ' AND email LIKE ?';
        queryParams.push(`%${filters.email}%`);
      }
      
      if (filters.role) {
        query += ' AND role = ?';
        queryParams.push(filters.role);
      }
      
      if (filters.status !== undefined) {
        query += ' AND status = ?';
        queryParams.push(filters.status);
      }
      
      query += ' ORDER BY created_at DESC';
      
      // 添加分页
      if (filters.limit) {
        const offset = (filters.page - 1) * filters.limit || 0;
        query += ' LIMIT ? OFFSET ?';
        queryParams.push(parseInt(filters.limit), parseInt(offset));
      }
      
      const [rows] = await pool.query(query, queryParams);
      return rows;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取用户
  static async findById(id) {
    try {
      const [rows] = await pool.query(
        'SELECT id, username, email, phone, role, status, avatar, last_login, created_at FROM users WHERE id = ?',
        [id]
      );
      return rows[0];
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  // 根据用户名查找用户（登录用）
  static async findByUsername(username) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM users WHERE username = ?',
        [username]
      );
      return rows[0];
    } catch (error) {
      console.error('查找用户失败:', error);
      throw error;
    }
  }

  // 更新用户信息
  static async update(id, userData) {
    try {
      let query = 'UPDATE users SET ';
      const queryParams = [];
      const updates = [];
      
      if (userData.username) {
        updates.push('username = ?');
        queryParams.push(userData.username);
      }
      
      if (userData.email) {
        updates.push('email = ?');
        queryParams.push(userData.email);
      }
      
      if (userData.phone) {
        updates.push('phone = ?');
        queryParams.push(userData.phone);
      }
      
      if (userData.role) {
        updates.push('role = ?');
        queryParams.push(userData.role);
      }
      
      if (userData.status !== undefined) {
        updates.push('status = ?');
        queryParams.push(userData.status);
      }
      
      if (userData.avatar) {
        updates.push('avatar = ?');
        queryParams.push(userData.avatar);
      }
      
      if (userData.password) {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(userData.password, salt);
        updates.push('password = ?');
        queryParams.push(hashedPassword);
      }
      
      if (updates.length === 0) {
        return false;
      }
      
      query += updates.join(', ') + ' WHERE id = ?';
      queryParams.push(id);
      
      const [result] = await pool.query(query, queryParams);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  // 更新最后登录时间
  static async updateLastLogin(id) {
    try {
      const [result] = await pool.query(
        'UPDATE users SET last_login = NOW() WHERE id = ?',
        [id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新最后登录时间失败:', error);
      throw error;
    }
  }

  // 更新用户密码
  static async updatePassword(id, hashedPassword) {
    try {
      const [result] = await pool.query(
        'UPDATE users SET password = ? WHERE id = ?',
        [hashedPassword, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新用户密码失败:', error);
      throw error;
    }
  }

  // 删除用户
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM users WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  }

  // 验证密码
  static async validatePassword(plainPassword, hashedPassword) {
    try {
      return await bcrypt.compare(plainPassword, hashedPassword);
    } catch (error) {
      console.error('密码验证失败:', error);
      return false;
    }
  }
}

module.exports = User;