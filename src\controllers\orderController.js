const Order = require('../models/orderModel');

// 获取所有订单
exports.getAllOrders = async (req, res) => {
  try {
    // 获取查询参数
    const filters = {
      order_number: req.query.order_number,
      name: req.query.name,
      status: req.query.status,
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 50
    };

    const orders = await Order.findAll(filters);
    res.status(200).json({
      success: true,
      count: orders.length,
      data: orders
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取订单列表失败',
      error: error.message
    });
  }
};

// 根据订单号获取订单
exports.getOrderByNumber = async (req, res) => {
  try {
    const order = await Order.findByOrderNumber(req.params.orderNumber);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }
    
    res.status(200).json({
      success: true,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取订单信息失败',
      error: error.message
    });
  }
};

// 获取单个订单
exports.getOrderById = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }
    
    res.status(200).json({
      success: true,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取订单信息失败',
      error: error.message
    });
  }
};

// 创建订单
exports.createOrder = async (req, res) => {
  try {
    const { name, email, phone, order_items, status } = req.body;
    
    // 生成订单号
    const order_number = Order.generateOrderNumber();
    
    const orderData = {
      order_number,
      name,
      email,
      phone,
      order_items,
      status: status || 'pending'
    };
    
    const orderId = await Order.create(orderData);
    const newOrder = await Order.findById(orderId);
    
    res.status(201).json({
      success: true,
      message: '订单创建成功',
      data: newOrder
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建订单失败',
      error: error.message
    });
  }
};

// 更新订单
exports.updateOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }
    
    const updated = await Order.update(req.params.id, req.body);
    
    if (updated) {
      const updatedOrder = await Order.findById(req.params.id);
      res.status(200).json({
        success: true,
        message: '订单更新成功',
        data: updatedOrder
      });
    } else {
      res.status(500).json({
        success: false,
        message: '订单更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新订单失败',
      error: error.message
    });
  }
};

// 更新订单状态
exports.updateOrderStatus = async (req, res) => {
  try {
    const { status } = req.body;
    
    if (!status) {
      return res.status(400).json({
        success: false,
        message: '请提供订单状态'
      });
    }
    
    const order = await Order.findById(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }
    
    const updated = await Order.updateStatus(req.params.id, status);
    
    if (updated) {
      const updatedOrder = await Order.findById(req.params.id);
      res.status(200).json({
        success: true,
        message: '订单状态更新成功',
        data: updatedOrder
      });
    } else {
      res.status(500).json({
        success: false,
        message: '订单状态更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新订单状态失败',
      error: error.message
    });
  }
};

// 删除订单
exports.deleteOrder = async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: '未找到该订单'
      });
    }
    
    const deleted = await Order.delete(req.params.id);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '订单删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '订单删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除订单失败',
      error: error.message
    });
  }
};