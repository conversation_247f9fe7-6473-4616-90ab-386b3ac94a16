{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, vShow as _vShow, createElementVNode as _createElementVNode, withDirectives as _withDirectives, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createBlock as _createBlock, createTextVNode as _createTextVNode, resolveDynamicComponent as _resolveDynamicComponent, toDisplayString as _toDisplayString } from \"vue\";\nimport _imports_0 from '../assets/logo.png';\nconst _hoisted_1 = {\n  class: \"app-container\"\n};\nconst _hoisted_2 = {\n  class: \"logo\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  src: _imports_0,\n  alt: \"logo\"\n};\nconst _hoisted_4 = {\n  class: \"header-left\"\n};\nconst _hoisted_5 = {\n  class: \"header-right\"\n};\nconst _hoisted_6 = {\n  class: \"user-info\"\n};\nconst _hoisted_7 = {\n  class: \"content-wrapper\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\");\n  const _component_el_menu = _resolveComponent(\"el-menu\");\n  const _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  const _component_el_aside = _resolveComponent(\"el-aside\");\n  const _component_el_breadcrumb_item = _resolveComponent(\"el-breadcrumb-item\");\n  const _component_el_breadcrumb = _resolveComponent(\"el-breadcrumb\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_el_dropdown_item = _resolveComponent(\"el-dropdown-item\");\n  const _component_el_dropdown_menu = _resolveComponent(\"el-dropdown-menu\");\n  const _component_el_dropdown = _resolveComponent(\"el-dropdown\");\n  const _component_el_header = _resolveComponent(\"el-header\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_el_main = _resolveComponent(\"el-main\");\n  const _component_el_container = _resolveComponent(\"el-container\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_container, {\n    class: \"layout-container\"\n  }, {\n    default: _withCtx(() => [_createCommentVNode(\" 左侧菜单 \"), _createVNode(_component_el_aside, {\n      width: $setup.isCollapse ? '64px' : '220px',\n      class: \"aside\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [!$setup.isCollapse ? (_openBlock(), _createElementBlock(\"img\", _hoisted_3)) : _createCommentVNode(\"v-if\", true), _withDirectives(_createElementVNode(\"h1\", null, \"后台管理系统\", 512 /* NEED_PATCH */), [[_vShow, !$setup.isCollapse]]), $setup.isCollapse ? (_openBlock(), _createBlock(_component_el_icon, {\n        key: 1,\n        size: \"30\",\n        color: \"#fff\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"Setting\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_el_scrollbar, null, {\n        default: _withCtx(() => [_createVNode(_component_el_menu, {\n          \"default-active\": $setup.activeMenu,\n          class: \"el-menu-vertical\",\n          collapse: $setup.isCollapse,\n          \"background-color\": \"#304156\",\n          \"text-color\": \"#bfcbd9\",\n          \"active-text-color\": \"#409EFF\",\n          router: \"\",\n          \"collapse-transition\": false\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_menu_item, {\n            index: \"/categories\"\n          }, {\n            title: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"分类管理\")])),\n            default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode($setup[\"Menu\"])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_menu_item, {\n            index: \"/orders\"\n          }, {\n            title: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"订单管理\")])),\n            default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode($setup[\"Document\"])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_menu_item, {\n            index: \"/users\"\n          }, {\n            title: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"用户管理\")])),\n            default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [_createVNode($setup[\"User\"])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"default-active\", \"collapse\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"width\"]), _createCommentVNode(\" 右侧内容 \"), _createVNode(_component_el_container, {\n      class: \"main-container\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 顶部导航 \"), _createVNode(_component_el_header, {\n        class: \"header\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_el_icon, {\n          class: \"fold-icon\",\n          onClick: $setup.toggleSidebar\n        }, {\n          default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent($setup.isCollapse ? 'Expand' : 'Fold')))]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_breadcrumb, {\n          separator: \"/\",\n          class: \"breadcrumb\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_breadcrumb_item, {\n            to: {\n              path: '/'\n            }\n          }, {\n            default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"首页\")])),\n            _: 1 /* STABLE */,\n            __: [3]\n          }), _createVNode(_component_el_breadcrumb_item, null, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentRoute), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_dropdown, {\n          trigger: \"click\"\n        }, {\n          dropdown: _withCtx(() => [_createVNode(_component_el_dropdown_menu, null, {\n            default: _withCtx(() => [_createVNode(_component_el_dropdown_item, {\n              onClick: $setup.handleProfile\n            }, {\n              default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"个人中心\")])),\n              _: 1 /* STABLE */,\n              __: [4]\n            }), _createVNode(_component_el_dropdown_item, {\n              divided: \"\",\n              onClick: $setup.handleLogout\n            }, {\n              default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"退出登录\")])),\n              _: 1 /* STABLE */,\n              __: [5]\n            })]),\n            _: 1 /* STABLE */\n          })]),\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_avatar, {\n            size: 30,\n            src: \"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"\n          }), _createElementVNode(\"span\", null, _toDisplayString($setup.username), 1 /* TEXT */), _createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode($setup[\"CaretBottom\"])]),\n            _: 1 /* STABLE */\n          })])]),\n          _: 1 /* STABLE */\n        })])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 内容区域 \"), _createVNode(_component_el_main, {\n        class: \"main\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_router_view)])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "src", "alt", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_container", "_createCommentVNode", "_component_el_aside", "width", "$setup", "isCollapse", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createBlock", "_component_el_icon", "size", "color", "_component_el_scrollbar", "_component_el_menu", "activeMenu", "collapse", "router", "_component_el_menu_item", "index", "title", "_withCtx", "_cache", "_component_el_header", "_hoisted_4", "onClick", "toggleSidebar", "_resolveDynamicComponent", "_component_el_breadcrumb", "separator", "_component_el_breadcrumb_item", "to", "path", "currentRoute", "_hoisted_5", "_component_el_dropdown", "trigger", "dropdown", "_component_el_dropdown_menu", "_component_el_dropdown_item", "handleProfile", "divided", "handleLogout", "_hoisted_6", "_component_el_avatar", "_toDisplayString", "username", "_component_el_main", "_hoisted_7", "_component_router_view"], "sources": ["D:\\admin\\202506\\乐高\\乐高后台\\后台管理系统v2\\后台管理系统\\ms\\src\\layout\\AppLayout.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-container class=\"layout-container\">\r\n      <!-- 左侧菜单 -->\r\n      <el-aside :width=\"isCollapse ? '64px' : '220px'\" class=\"aside\">\r\n        <div class=\"logo\">\r\n          <img src=\"../assets/logo.png\" alt=\"logo\" v-if=\"!isCollapse\" />\r\n          <h1 v-show=\"!isCollapse\">后台管理系统</h1>\r\n          <el-icon v-if=\"isCollapse\" size=\"30\" color=\"#fff\"><Setting /></el-icon>\r\n        </div>\r\n        <el-scrollbar>\r\n          <el-menu\r\n            :default-active=\"activeMenu\"\r\n            class=\"el-menu-vertical\"\r\n            :collapse=\"isCollapse\"\r\n            background-color=\"#304156\"\r\n            text-color=\"#bfcbd9\"\r\n            active-text-color=\"#409EFF\"\r\n            router\r\n            :collapse-transition=\"false\"\r\n          >\r\n         \r\n            \r\n            <el-menu-item index=\"/categories\">\r\n              <el-icon><Menu /></el-icon>\r\n              <template #title>分类管理</template>\r\n            </el-menu-item>\r\n            \r\n            <el-menu-item index=\"/orders\">\r\n              <el-icon><Document /></el-icon>\r\n              <template #title>订单管理</template>\r\n            </el-menu-item>\r\n            \r\n            <el-menu-item index=\"/users\">\r\n              <el-icon><User /></el-icon>\r\n              <template #title>用户管理</template>\r\n            </el-menu-item>\r\n          </el-menu>\r\n        </el-scrollbar>\r\n      </el-aside>\r\n      \r\n      <!-- 右侧内容 -->\r\n      <el-container class=\"main-container\">\r\n        <!-- 顶部导航 -->\r\n        <el-header class=\"header\">\r\n          <div class=\"header-left\">\r\n            <el-icon class=\"fold-icon\" @click=\"toggleSidebar\">\r\n              <component :is=\"isCollapse ? 'Expand' : 'Fold'\"></component>\r\n            </el-icon>\r\n            <el-breadcrumb separator=\"/\" class=\"breadcrumb\">\r\n              <el-breadcrumb-item :to=\"{ path: '/' }\">首页</el-breadcrumb-item>\r\n              <el-breadcrumb-item>{{ currentRoute }}</el-breadcrumb-item>\r\n            </el-breadcrumb>\r\n          </div>\r\n          <div class=\"header-right\">\r\n            <el-dropdown trigger=\"click\">\r\n              <div class=\"user-info\">\r\n                <el-avatar :size=\"30\" src=\"https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png\"></el-avatar>\r\n                <span>{{ username }}</span>\r\n                <el-icon><CaretBottom /></el-icon>\r\n              </div>\r\n              <template #dropdown>\r\n                <el-dropdown-menu>\r\n                  <el-dropdown-item @click=\"handleProfile\">个人中心</el-dropdown-item>\r\n                  <el-dropdown-item divided @click=\"handleLogout\">退出登录</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </template>\r\n            </el-dropdown>\r\n          </div>\r\n        </el-header>\r\n        \r\n        <!-- 内容区域 -->\r\n        <el-main class=\"main\">\r\n          <div class=\"content-wrapper\">\r\n            <router-view />\r\n          </div>\r\n        </el-main>\r\n      </el-container>\r\n    </el-container>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { \r\n  User,\r\n  Menu,\r\n  Grid,\r\n  Document,\r\n  DataBoard,\r\n  Setting,\r\n  CaretBottom, \r\n  Fold, \r\n  Expand \r\n} from '@element-plus/icons-vue'\r\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\nconst isCollapse = ref(false)\r\nconst username = ref(localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).username : '管理员')\r\n\r\nconst activeMenu = computed(() => {\r\n  return route.path\r\n})\r\n\r\nconst currentRoute = computed(() => {\r\n  return route.meta.title || '仪表盘'\r\n})\r\n\r\nconst toggleSidebar = () => {\r\n  isCollapse.value = !isCollapse.value\r\n}\r\n\r\nconst handleProfile = () => {\r\n  ElMessage.info('个人中心功能开发中...')\r\n}\r\n\r\nconst handleLogout = () => {\r\n  ElMessageBox.confirm('确定要退出登录吗?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => {\r\n    localStorage.removeItem('token')\r\n    localStorage.removeItem('userInfo')\r\n    router.push('/login')\r\n    ElMessage.success('已退出登录')\r\n  }).catch(() => {})\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  height: 100vh;\r\n  width: 100%;\r\n}\r\n\r\n.layout-container {\r\n  height: 100%;\r\n}\r\n\r\n.aside {\r\n  background-color: #304156;\r\n  transition: width 0.3s;\r\n  overflow: hidden;\r\n}\r\n\r\n.logo {\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #2b3649;\r\n  color: #fff;\r\n  padding: 0 15px;\r\n}\r\n\r\n.logo img {\r\n  width: 30px;\r\n  height: 30px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.logo h1 {\r\n  margin: 0;\r\n  color: #fff;\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  white-space: nowrap;\r\n}\r\n\r\n.el-menu-vertical {\r\n  border-right: none;\r\n}\r\n\r\n.el-menu-vertical:not(.el-menu--collapse) {\r\n  width: 220px;\r\n}\r\n\r\n.main-container {\r\n  background-color: #f0f2f5;\r\n}\r\n\r\n.header {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #e8eaec;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  box-shadow: 0 1px 4px rgba(0,21,41,.08);\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.fold-icon {\r\n  font-size: 18px;\r\n  cursor: pointer;\r\n  margin-right: 20px;\r\n  color: #666;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.fold-icon:hover {\r\n  color: #409EFF;\r\n}\r\n\r\n.breadcrumb {\r\n  font-size: 14px;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  padding: 5px 10px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.user-info:hover {\r\n  background-color: #f5f5f5;\r\n}\r\n\r\n.user-info span {\r\n  margin: 0 8px;\r\n  font-size: 14px;\r\n  color: #333;\r\n}\r\n\r\n.main {\r\n  padding: 20px;\r\n  background-color: #f0f2f5;\r\n  min-height: calc(100vh - 60px);\r\n}\r\n\r\n.content-wrapper {\r\n  background-color: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\r\n  min-height: calc(100vh - 100px);\r\n}\r\n</style> \r\n"], "mappings": ";OAMeA,UAAwB;;EALhCC,KAAK,EAAC;AAAe;;EAIfA,KAAK,EAAC;AAAM;;;EACVC,GAAwB,EAAxBF,UAAwB;EAACG,GAAG,EAAC;;;EAuC7BF,KAAK,EAAC;AAAa;;EASnBA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAW;;EAiBrBA,KAAK,EAAC;AAAiB;;;;;;;;;;;;;;;;;uBAxEpCG,mBAAA,CA8EM,OA9ENC,UA8EM,GA7EJC,YAAA,CA4EeC,uBAAA;IA5EDN,KAAK,EAAC;EAAkB;sBACpC,MAAa,CAAbO,mBAAA,UAAa,EACbF,YAAA,CAmCWG,mBAAA;MAnCAC,KAAK,EAAEC,MAAA,CAAAC,UAAU;MAAqBX,KAAK,EAAC;;wBACrD,MAIM,CAJNY,mBAAA,CAIM,OAJNC,UAIM,G,CAH4CH,MAAA,CAAAC,UAAU,I,cAA1DR,mBAAA,CAA8D,OAA9DW,UAA8D,K,mDAC9DF,mBAAA,CAAoC,YAAX,QAAM,0B,UAAlBF,MAAA,CAAAC,UAAU,E,GACRD,MAAA,CAAAC,UAAU,I,cAAzBI,YAAA,CAAuEC,kBAAA;;QAA5CC,IAAI,EAAC,IAAI;QAACC,KAAK,EAAC;;0BAAO,MAAW,CAAXb,YAAA,CAAWK,MAAA,a;;iDAE/DL,YAAA,CA4Bec,uBAAA;0BA3Bb,MA0BU,CA1BVd,YAAA,CA0BUe,kBAAA;UAzBP,gBAAc,EAAEV,MAAA,CAAAW,UAAU;UAC3BrB,KAAK,EAAC,kBAAkB;UACvBsB,QAAQ,EAAEZ,MAAA,CAAAC,UAAU;UACrB,kBAAgB,EAAC,SAAS;UAC1B,YAAU,EAAC,SAAS;UACpB,mBAAiB,EAAC,SAAS;UAC3BY,MAAM,EAAN,EAAM;UACL,qBAAmB,EAAE;;4BAItB,MAGe,CAHflB,YAAA,CAGemB,uBAAA;YAHDC,KAAK,EAAC;UAAa;YAEpBC,KAAK,EAAAC,QAAA,CAAC,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;8BADrB,MAA2B,CAA3BvB,YAAA,CAA2BW,kBAAA;gCAAlB,MAAQ,CAARX,YAAA,CAAQK,MAAA,U;;;;cAInBL,YAAA,CAGemB,uBAAA;YAHDC,KAAK,EAAC;UAAS;YAEhBC,KAAK,EAAAC,QAAA,CAAC,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;8BADrB,MAA+B,CAA/BvB,YAAA,CAA+BW,kBAAA;gCAAtB,MAAY,CAAZX,YAAA,CAAYK,MAAA,c;;;;cAIvBL,YAAA,CAGemB,uBAAA;YAHDC,KAAK,EAAC;UAAQ;YAEfC,KAAK,EAAAC,QAAA,CAAC,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;8BADrB,MAA2B,CAA3BvB,YAAA,CAA2BW,kBAAA;gCAAlB,MAAQ,CAARX,YAAA,CAAQK,MAAA,U;;;;;;;;;;kCAOzBH,mBAAA,UAAa,EACbF,YAAA,CAmCeC,uBAAA;MAnCDN,KAAK,EAAC;IAAgB;wBAClC,MAAa,CAAbO,mBAAA,UAAa,EACbF,YAAA,CAyBYwB,oBAAA;QAzBD7B,KAAK,EAAC;MAAQ;0BACvB,MAQM,CARNY,mBAAA,CAQM,OARNkB,UAQM,GAPJzB,YAAA,CAEUW,kBAAA;UAFDhB,KAAK,EAAC,WAAW;UAAE+B,OAAK,EAAErB,MAAA,CAAAsB;;4BACjC,MAA4D,E,cAA5DjB,YAAA,CAA4DkB,wBAAA,CAA5CvB,MAAA,CAAAC,UAAU,wB;;YAE5BN,YAAA,CAGgB6B,wBAAA;UAHDC,SAAS,EAAC,GAAG;UAACnC,KAAK,EAAC;;4BACjC,MAA+D,CAA/DK,YAAA,CAA+D+B,6BAAA;YAA1CC,EAAE,EAAE;cAAAC,IAAA;YAAA;UAAa;8BAAE,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;cAC1CvB,YAAA,CAA2D+B,6BAAA;8BAAvC,MAAkB,C,kCAAf1B,MAAA,CAAA6B,YAAY,iB;;;;cAGvC3B,mBAAA,CAcM,OAdN4B,UAcM,GAbJnC,YAAA,CAYcoC,sBAAA;UAZDC,OAAO,EAAC;QAAO;UAMfC,QAAQ,EAAAhB,QAAA,CACjB,MAGmB,CAHnBtB,YAAA,CAGmBuC,2BAAA;8BAFjB,MAAgE,CAAhEvC,YAAA,CAAgEwC,2BAAA;cAA7Cd,OAAK,EAAErB,MAAA,CAAAoC;YAAa;gCAAE,MAAIlB,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;gBAC7CvB,YAAA,CAAuEwC,2BAAA;cAArDE,OAAO,EAAP,EAAO;cAAEhB,OAAK,EAAErB,MAAA,CAAAsC;;gCAAc,MAAIpB,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;;;;4BARxD,MAIM,CAJNhB,mBAAA,CAIM,OAJNqC,UAIM,GAHJ5C,YAAA,CAA4G6C,oBAAA;YAAhGjC,IAAI,EAAE,EAAE;YAAEhB,GAAG,EAAC;cAC1BW,mBAAA,CAA2B,cAAAuC,gBAAA,CAAlBzC,MAAA,CAAA0C,QAAQ,kBACjB/C,YAAA,CAAkCW,kBAAA;8BAAzB,MAAe,CAAfX,YAAA,CAAeK,MAAA,iB;;;;;;UAYhCH,mBAAA,UAAa,EACbF,YAAA,CAIUgD,kBAAA;QAJDrD,KAAK,EAAC;MAAM;0BACnB,MAEM,CAFNY,mBAAA,CAEM,OAFN0C,UAEM,GADJjD,YAAA,CAAekD,sBAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}